import React, { useState, useEffect } from 'react';
import performanceMonitor from '@utils/performanceMonitor';

const PerformanceDashboard = () => {
  const [metrics, setMetrics] = useState({
    fps: 0,
    memoryUsage: 0,
    animationCount: 0,
    frameTime: 0
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(performanceMonitor.getMetrics());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Toggle visibility with Ctrl+Shift+P
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  if (!isVisible || !import.meta.env.DEV) {
    return null;
  }

  const getFPSColor = (fps) => {
    if (fps >= 55) return 'text-green-500';
    if (fps >= 45) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getMemoryColor = (memory) => {
    if (memory < 50) return 'text-green-500';
    if (memory < 100) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getAnimationColor = (count) => {
    if (count < 5) return 'text-green-500';
    if (count < 10) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className="fixed top-4 right-4 bg-black/90 text-white p-4 rounded-lg shadow-lg z-50 font-mono text-sm min-w-[200px]">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-white font-bold">Performance Monitor</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between">
          <span>FPS:</span>
          <span className={getFPSColor(metrics.fps)}>
            {metrics.fps}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Memory:</span>
          <span className={getMemoryColor(metrics.memoryUsage)}>
            {metrics.memoryUsage}MB
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Animations:</span>
          <span className={getAnimationColor(metrics.animationCount)}>
            {metrics.animationCount}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Frame Time:</span>
          <span className="text-blue-400">
            {metrics.frameTime.toFixed(2)}ms
          </span>
        </div>
      </div>
      
      <div className="mt-3 pt-2 border-t border-gray-600">
        <div className="text-xs text-gray-400">
          Press Ctrl+Shift+P to toggle
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
