import { useState, useEffect, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { motion, AnimatePresence } from 'framer-motion';
import { HiQrCode, HiTrash, HiCheck } from 'react-icons/hi2';
import { FaSpinner } from 'react-icons/fa';
import BarcodeScanner from '../../Backages/components/BarcodeScanner';
import CardTypeCarousel from '../../Backages/components/CardTypeCarousel';
import './BatchCardCreationModal.css';

const BatchCardCreationModal = ({ isOpen, onClose, onBatchCreated, toast }) => {
  const [scannedCards, setScannedCards] = useState([]);
  const [showScanner, setShowScanner] = useState(false);
  const [selectedCardType, setSelectedCardType] = useState(null);
  const [cardTypes, setCardTypes] = useState([]);
  const [isCreating, setIsCreating] = useState(false);
  const [currentStep, setCurrentStep] = useState(1); // 1: Select Type, 2: Scan Cards, 3: Review
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  // Fetch card types when modal opens
  useEffect(() => {
    const fetchCardTypes = async () => {
      try {
        const response = await fetch(`${backendUrl}/card-types`, {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        });

        if (!response.ok) throw new Error("Failed to fetch card types.");

        const data = await response.json();
        const types = data.data || data;
        
        const formattedTypes = Array.isArray(types) ? types.map((type) => ({
          id: type.id,
          name: type.name,
          type_of_connection: type.type_of_connection,
          setting: type.setting
        })) : [];

        setCardTypes(formattedTypes);
      } catch (error) {
        console.error("Error fetching card types:", error);
        setCardTypes([]);
      }
    };

    if (isOpen) {
      fetchCardTypes();
    }
  }, [isOpen, backendUrl, token]);

  const handleCardTypeSelect = (cardType) => {
    setSelectedCardType(cardType);
  };

  const handleBarcodeScanned = async (scannedData) => {
    // Trim and validate the scanned data
    const trimmedData = scannedData.trim();

    if (!trimmedData) {
      toast.current.show({
        severity: 'warn',
        summary: 'Invalid Barcode',
        detail: 'Scanned barcode is empty or invalid',
        life: 3000
      });
      return;
    }

    // Check if barcode already exists in the current batch
    const existsInBatch = scannedCards.some(card => card.number === trimmedData);

    if (existsInBatch) {
      toast.current.show({
        severity: 'warn',
        summary: 'Duplicate in Batch',
        detail: `Card with number ${trimmedData} is already in this batch`,
        life: 3000
      });
      return;
    }

    // Check if barcode already exists in the database
    try {
      const response = await fetch(`${backendUrl}/cards/check-duplicate/${encodeURIComponent(trimmedData)}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.exists) {
          toast.current.show({
            severity: 'error',
            summary: 'Card Already Exists',
            detail: `Card with number ${trimmedData} already exists in the database`,
            life: 4000
          });
          return;
        }
      }
      // If the API call fails or endpoint doesn't exist, continue with adding the card
      // This ensures the feature works even if the duplicate check API is not available
    } catch (error) {
      console.warn('Duplicate check failed, proceeding with card addition:', error);
      // Continue with adding the card even if duplicate check fails
    }

    // Add new card to the batch
    const newCard = {
      id: Date.now() + Math.random(), // Temporary ID for UI
      name: trimmedData, // Use barcode as card name
      number: trimmedData,
      type: selectedCardType.id
    };

    setScannedCards(prev => [...prev, newCard]);

    toast.current.show({
      severity: 'success',
      summary: 'Card Added',
      detail: `Card ${trimmedData} added to batch (${scannedCards.length + 1} total)`,
      life: 2000
    });

    // Scanner remains open for continuous scanning - no need to close
  };

  const removeCard = (cardId) => {
    setScannedCards(prev => prev.filter(card => card.id !== cardId));
  };

  const handleNextStep = () => {
    if (currentStep === 1 && selectedCardType) {
      setCurrentStep(2);
      setShowScanner(true);
    } else if (currentStep === 2 && scannedCards.length > 0) {
      setCurrentStep(3);
      setShowScanner(false);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep === 3) {
      setCurrentStep(2);
    } else if (currentStep === 2) {
      setCurrentStep(1);
      setShowScanner(false);
    }
  };

  const createBatch = async () => {
    if (scannedCards.length === 0 || !selectedCardType) {
      toast.current.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please scan at least one card and select a card type',
        life: 3000
      });
      return;
    }

    setIsCreating(true);

    try {
      // Create cards one by one using the existing API
      const results = [];
      
      for (const card of scannedCards) {
        const response = await fetch(`${backendUrl}/create_cards`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            name: card.name,
            number: card.number,
            type: card.type,
          }),
        });

        const data = await response.json();
        
        if (response.ok) {
          results.push({ success: true, card: card.number });
        } else {
          results.push({ success: false, card: card.number, error: data.message });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        toast.current.show({
          severity: 'success',
          summary: 'Batch Created',
          detail: `Successfully created ${successCount} cards${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
          life: 4000
        });
        
        if (onBatchCreated) onBatchCreated();
        handleClose();
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Batch Creation Failed',
          detail: 'No cards were created successfully',
          life: 3000
        });
      }

    } catch (error) {
      console.error('Error creating batch:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while creating the batch',
        life: 3000
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    setScannedCards([]);
    setSelectedCardType(null);
    setCurrentStep(1);
    setShowScanner(false);
    setIsCreating(false);
    onClose();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Select Card Type</h3>
              <p className="text-gray-600 text-sm">Choose the type for all cards in this batch</p>
            </div>
            
            <CardTypeCarousel
              cardTypes={cardTypes}
              onCardTypeSelect={handleCardTypeSelect}
              selectedCardType={selectedCardType}
              itemsPerView={{
                desktop: 3,
                tablet: 2,
                mobile: 1
              }}
            />
            
            {cardTypes.length === 0 && (
              <div className="text-center py-4">
                <p className="text-red-500 text-sm">No card types available</p>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Scan Cards</h3>
              <p className="text-gray-600 text-sm">
                Scan barcodes to add cards to your batch. Each barcode will be used as the card name.
              </p>
            </div>

            {/* Scanned Cards Counter */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">{scannedCards.length}</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-800">Cards Scanned</p>
                    <p className="text-sm text-gray-600">
                      {scannedCards.length === 0 ? 'No cards scanned yet' : 
                       scannedCards.length === 1 ? '1 card ready' : 
                       `${scannedCards.length} cards ready`}
                    </p>
                  </div>
                </div>
                <motion.button
                  onClick={() => setShowScanner(true)}
                  className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-all duration-200 flex items-center gap-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <HiQrCode className="text-lg" />
                  {scannedCards.length === 0 ? 'Start Scanning' : 'Scan More'}
                </motion.button>
              </div>
            </div>

            {/* Scanned Cards List */}
            {scannedCards.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-gray-800">Scanned Cards:</h4>
                <div className="max-h-48 overflow-y-auto space-y-2">
                  {scannedCards.map((card, index) => (
                    <motion.div
                      key={card.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-3"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 text-sm font-bold">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-800">{card.name}</p>
                          <p className="text-sm text-gray-600">Number: {card.number}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => removeCard(card.id)}
                        className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200"
                        title="Remove card"
                      >
                        <HiTrash className="text-lg" />
                      </button>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Review Batch</h3>
              <p className="text-gray-600 text-sm">Review your batch before creating the cards</p>
            </div>

            {/* Batch Summary */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Card Type</p>
                  <p className="font-medium text-gray-800">{selectedCardType?.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Cards</p>
                  <p className="font-medium text-gray-800">{scannedCards.length}</p>
                </div>
              </div>
            </div>

            {/* Cards Preview */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-800">Cards to Create:</h4>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {scannedCards.map((card, index) => (
                  <div
                    key={card.id}
                    className="flex items-center gap-3 bg-white border border-gray-200 rounded-lg p-3"
                  >
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 text-sm font-bold">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-800">{card.name}</p>
                      <p className="text-sm text-gray-600">Number: {card.number}</p>
                    </div>
                    <HiCheck className="text-green-500 text-lg" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Dialog
        header={
          <div className="flex items-center gap-3">
            <div className="w-1 h-8 bg-gradient-to-b from-[#00c3ac] to-[#02aa96] rounded-full"></div>
            <h2 className="text-xl font-semibold text-gray-900">Create Card Batch</h2>
          </div>
        }
        visible={isOpen}
        onHide={handleClose}
        style={{
          width: "90vw",
          maxWidth: "800px",
          borderRadius: "16px",
          overflow: "hidden"
        }}
        modal
        className="batch-card-creation-dialog"
        contentClassName="p-0"
      >
        <div className="p-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center gap-4">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold transition-all duration-300 ${
                    currentStep >= step 
                      ? 'bg-[#00c3ac] text-white' 
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <div className={`w-12 h-1 mx-2 transition-all duration-300 ${
                      currentStep > step ? 'bg-[#00c3ac]' : 'bg-gray-200'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6 border-t border-gray-200 mt-8">
            <div className="flex gap-3">
              {currentStep > 1 && (
                <motion.button
                  type="button"
                  onClick={handlePreviousStep}
                  disabled={isCreating}
                  className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Previous
                </motion.button>
              )}
            </div>

            <div className="flex gap-3">
              <motion.button
                type="button"
                onClick={handleClose}
                disabled={isCreating}
                className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Cancel
              </motion.button>

              {currentStep < 3 ? (
                <motion.button
                  type="button"
                  onClick={handleNextStep}
                  disabled={
                    (currentStep === 1 && !selectedCardType) ||
                    (currentStep === 2 && scannedCards.length === 0) ||
                    isCreating
                  }
                  className="px-6 py-3 bg-[#00c3ac] hover:bg-[#02aa96] text-white rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {currentStep === 1 ? 'Start Scanning' : 'Review Batch'}
                </motion.button>
              ) : (
                <motion.button
                  type="button"
                  onClick={createBatch}
                  disabled={isCreating || scannedCards.length === 0}
                  className="px-6 py-3 bg-[#00c3ac] hover:bg-[#02aa96] text-white rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl flex items-center gap-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {isCreating ? (
                    <>
                      <FaSpinner className="animate-spin" />
                      Creating Batch...
                    </>
                  ) : (
                    <>
                      <HiCheck />
                      Create Batch ({scannedCards.length} cards)
                    </>
                  )}
                </motion.button>
              )}
            </div>
          </div>
        </div>
      </Dialog>

      {/* Barcode Scanner Modal */}
      {showScanner && (
        <BarcodeScanner
          isOpen={showScanner}
          onClose={() => setShowScanner(false)}
          onBarcodeScanned={handleBarcodeScanned}
          continuousMode={true}
        />
      )}
    </>
  );
};

export default BatchCardCreationModal;
