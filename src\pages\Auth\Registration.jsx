import { useRef } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { Dropdown } from 'primereact/dropdown';
import SideImage from './SideImage';
import LocationPicker from '../../components/LocationPicker';
import { useRegisterMutation } from '../../quires';
import { getFormErrorMessage } from '@utils/helper';

function Registration() {
  const { t } = useTranslation("auth");
  const { formState: { errors }, handleSubmit, control, setError, watch, trigger, clearErrors } = useForm({
    defaultValues: {
      countryCode: { name: 'Jordan', code: '+962', flag: '🇯🇴' }
    },
    mode: 'onChange' // Enable real-time validation
  });
  const register = useRegisterMutation();
  const toast = useRef(null);
  const navigate = useNavigate();

  // Watch the selected country code
  const selectedCountry = watch('countryCode');

  const countries = [
    // Middle East & Arab Countries
    { name: 'Jordan', code: '+962', flag: '🇯🇴' },
    { name: 'Saudi Arabia', code: '+966', flag: '🇸🇦' },
    { name: 'UAE', code: '+971', flag: '🇦🇪' },
    { name: 'Kuwait', code: '+965', flag: '🇰🇼' },
    { name: 'Qatar', code: '+974', flag: '🇶🇦' },
    { name: 'Bahrain', code: '+973', flag: '🇧🇭' },
    { name: 'Oman', code: '+968', flag: '🇴🇲' },
    { name: 'Iraq', code: '+964', flag: '🇮🇶' },
    { name: 'Syria', code: '+963', flag: '🇸🇾' },
    { name: 'Lebanon', code: '+961', flag: '🇱🇧' },
    { name: 'Turkey', code: '+90', flag: '🇹🇷' },
    { name: 'Egypt', code: '+20', flag: '🇪🇬' },
    { name: 'Palestine', code: '+970', flag: '🇵🇸' },
    { name: 'Yemen', code: '+967', flag: '🇾🇪' },
    { name: 'Morocco', code: '+212', flag: '🇲🇦' },
    { name: 'Tunisia', code: '+216', flag: '🇹🇳' },
    { name: 'Algeria', code: '+213', flag: '🇩🇿' },
    
    // International
    { name: 'United States', code: '+1', flag: '🇺🇸' },
    { name: 'United Kingdom', code: '+44', flag: '🇬🇧' },
    { name: 'France', code: '+33', flag: '🇫🇷' },
    { name: 'Germany', code: '+49', flag: '🇩🇪' },
    { name: 'Canada', code: '+1', flag: '🇨🇦' },
    { name: 'Australia', code: '+61', flag: '🇦🇺' },
    { name: 'India', code: '+91', flag: '🇮🇳' },
    { name: 'Pakistan', code: '+92', flag: '🇵🇰' },
    { name: 'Bangladesh', code: '+880', flag: '🇧🇩' },
    { name: 'Malaysia', code: '+60', flag: '🇲🇾' },
    { name: 'Singapore', code: '+65', flag: '🇸🇬' },
    { name: 'Philippines', code: '+63', flag: '🇵🇭' },
    { name: 'Indonesia', code: '+62', flag: '🇮🇩' },
    { name: 'Thailand', code: '+66', flag: '🇹🇭' }
  ];

  // Format phone number as user types based on country
  const formatPhoneNumber = (value, countryCode) => {
    if (!value) return '';
    
    // Remove all non-numeric characters
    const cleaned = value.replace(/\D/g, '');
    
    // Apply formatting based on country
    switch (countryCode) {
      case '+1': // US/Canada format: (*************
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      
      case '+44': // UK format: 0123 456 7890
        if (cleaned.length <= 4) return cleaned;
        if (cleaned.length <= 7) return `${cleaned.slice(0, 4)} ${cleaned.slice(4)}`;
        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7, 11)}`;
      
      case '+962': // Jordan format: 07 9999 9999 or 7 9999 9999
        if (cleaned.startsWith('0')) {
          // Format with leading 0: 07 9999 9999
          if (cleaned.length <= 2) return cleaned;
          if (cleaned.length <= 6) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
          return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 6)} ${cleaned.slice(6, 10)}`;
        } else {
          // Format without leading 0: 7 9999 9999
          if (cleaned.length <= 1) return cleaned;
          if (cleaned.length <= 5) return `${cleaned.slice(0, 1)} ${cleaned.slice(1)}`;
          return `${cleaned.slice(0, 1)} ${cleaned.slice(1, 5)} ${cleaned.slice(5, 9)}`;
        }
      case '+963': // Syria
      case '+961': // Lebanon
        if (cleaned.length <= 2) return cleaned;
        if (cleaned.length <= 6) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
        return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 6)} ${cleaned.slice(6, 10)}`;
      
      case '+966': // Saudi Arabia: 5X XXX XXXX
      case '+971': // UAE: 5X XXX XXXX  
      case '+965': // Kuwait: 9XXX XXXX
      case '+974': // Qatar: 9XXX XXXX
      case '+973': // Bahrain: 9XXX XXXX
      case '+968': // Oman: 9XXX XXXX
        if (cleaned.length <= 2) return cleaned;
        if (cleaned.length <= 5) return `${cleaned.slice(0, 2)} ${cleaned.slice(2)}`;
        return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 9)}`;
      
      case '+964': // Iraq: 07XX XXX XXXX
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 10)}`;
      
      case '+90': // Turkey: (5XX) XXX XX XX
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
        if (cleaned.length <= 8) return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)} ${cleaned.slice(6, 8)} ${cleaned.slice(8, 10)}`;
      
      default: // Generic formatting: XXX XXX XXXX
        if (cleaned.length <= 3) return cleaned;
        if (cleaned.length <= 6) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    }
  };

  // Validate phone number based on country
  const validatePhoneNumber = (phone, countryCode) => {
    if (!phone) return false;
    
    const cleaned = phone.replace(/\D/g, '');
    
    switch (countryCode) {
      case '+1': // US/Canada: 10 digits
        return cleaned.length === 10;
      case '+44': // UK: 10-11 digits
        return cleaned.length >= 10 && cleaned.length <= 11;
      case '+962': // Jordan: 9 digits (without 0) or 10 digits (with 0)
        if (cleaned.length === 10 && cleaned.startsWith('0')) {
          return true; // Accept 0795888879 format
        }
        if (cleaned.length === 9 && !cleaned.startsWith('0')) {
          return true; // Accept 795888879 format
        }
        return false;
      case '+963': // Syria: 9 digits
      case '+961': // Lebanon: 8 digits
        return cleaned.length >= 8 && cleaned.length <= 9;
      case '+966': // Saudi Arabia: 9 digits
      case '+971': // UAE: 9 digits
      case '+965': // Kuwait: 8 digits
      case '+974': // Qatar: 8 digits
      case '+973': // Bahrain: 8 digits
      case '+968': // Oman: 8 digits
        return cleaned.length >= 8 && cleaned.length <= 9;
      case '+964': // Iraq: 10 digits
        return cleaned.length === 10;
      case '+90': // Turkey: 10 digits
        return cleaned.length === 10;
      default:
        return cleaned.length >= 7 && cleaned.length <= 15;
    }
  };

  const countryTemplate = (option) => {
    return (
      <div className="flex items-center">
        <span className="mr-2">{option.flag}</span>
        <span>{option.name} ({option.code})</span>
      </div>
    );
  };

  const selectedCountryTemplate = (option) => {
    if (option) {
      return (
        <div className="flex items-center">
          <span className="mr-2">{option.flag}</span>
          <span>{option.code}</span>
        </div>
      );
    }
    return <span>Select Country</span>;
  };

  const onSubmit = async (data) => {
    try {
      // Clear any existing errors before submission
      clearErrors();

      // Validate location data before submission
      if (!data.companyLocation || !data.companyLocation.coordinates) {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Please select a valid company location on the map',
          life: 3000
        });
        return;
      }

      // Combine country code and phone number for submission
      const formattedData = {
        ...data,
        phone: `${data.countryCode.code}${data.phone.replace(/\D/g, '')}`, // Clean phone and add country code
        countryCode: data.countryCode.code, // Keep country code separate if needed
        // Handle location data
        Address: data.companyLocation?.address || data.companyLocation?.formatted || '',
        latitude: data.companyLocation?.coordinates?.lat || null,
        longitude: data.companyLocation?.coordinates?.lng || null
      };

      console.log('📤 Final Registration Data to be sent:', formattedData);
      console.log('📍 Location Data in request:', {
        Address: formattedData.Address,
        latitude: formattedData.latitude,
        longitude: formattedData.longitude
      });

      const response = await register.mutateAsync(formattedData);
      
      // Check if email verification is required
      if (response.email_verification_required) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: response.message || 'Registration successful! Please check your email to verify your account.',
          life: 5000
        });
        
        // Redirect to email verification page
        navigate(`/verify-email?email=${encodeURIComponent(data.email)}`);
      } else {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Account Created Successfully',
          life: 3000
        });
        navigate('/login');
      }
    } catch (error) {
      if (error.response?.data?.details) {
        const serverErrors = error.response.data.details;
        Object.keys(serverErrors).forEach(field => {
          const message = serverErrors[field][0];
          setError(field, {
            type: 'manual',
            message: message,
          });

          if (toast.current) {
            toast.current.show({
              severity: 'error',
              summary: 'Error',
              detail: message,
              life: 3000
            });
          }
        });
      } else {
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: error.response?.data?.error || 'Registration failed',
          life: 3000
        });
      }
    }
  };

  // Function to handle field changes and clear errors
  const handleFieldChange = (fieldName, value, onChange) => {
    // Clear error for this field if it exists
    if (errors[fieldName]) {
      clearErrors(fieldName);
    }
    onChange(value);
  };

  return (
    <div className='w-full md:h-[100vh] overflow-hidden flex dark:bg-gray-900'>
      <Toast ref={toast} />
      <SideImage />
      <div className='w-full sm:w-7/12 h-full p-6 md:p-12 flex flex-col justify-center dark:bg-gray-900'>
        <h1 className='text-3xl font-bold pb-6 md:mb-12 dark:text-white'>{t('registration_title')}</h1>
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-wrap">

          {/* Name Field */}
          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-1">
              <label className="form-label mb-2 text-[#696F79] dark:text-gray-300">{t('inputs.name')}</label>
              <span className="p-float-label mt-2">
                <Controller name="name" control={control}
                  rules={{
                    required: t('messages.required'),
                    minLength: {
                      value: 3,
                      message: t('Please enter a name with at least 3 characters.', { count: 3 })
                    },
                    maxLength: {
                      value: 50,
                      message: t('messages.max_length', { count: 32 })
                    }
                  }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.name')}
                      maxLength={50}
                      onChange={(e) => handleFieldChange('name', e.target.value, field.onChange)}
                      className={`w-full text-[#696F79] dark:text-gray-300 dark:bg-gray-800 dark:border-gray-600 p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('name', errors)}
            </div>
          </div>

          {/* Improved Phone Field */}
          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79] dark:text-gray-300">{t('inputs.phone')}</label>
              
              <div className="flex mt-2 gap-2">
                {/* Country Code Dropdown */}
                <Controller
                  name="countryCode"
                  control={control}
                  rules={{ required: t('messages.country_required', { default: 'Please select a country' }) }}
                  render={({ field, fieldState }) => (
                    <Dropdown
                      id={field.name}
                      value={field.value}
                      onChange={(e) => handleFieldChange('countryCode', e.value, field.onChange)}
                      options={countries}
                      optionLabel="name"
                      placeholder="Select"
                      itemTemplate={countryTemplate}
                      valueTemplate={selectedCountryTemplate}
                      className={`${classNames({ 'p-invalid': fieldState.invalid })} dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300`}
                      style={{ minWidth: '140px', maxWidth: '160px' }}
                      filter
                      filterBy="name,code"
                      showClear={false}
                    />
                  )}
                />

                {/* Phone Number Input */}
                <Controller
                  name="phone"
                  control={control}
                  rules={{
                    required: t('messages.required'),
                    validate: (value) => {
                      const countryCode = selectedCountry?.code || '+962';
                      if (!validatePhoneNumber(value, countryCode)) {
                        return t('invalid phone format', { 
                          default: 'Invalid phone number format for selected country' 
                        });
                      }
                      return true;
                    }
                  }}
                  render={({ field, fieldState }) => {
                    const countryCode = selectedCountry?.code || '+962';
                    
                    return (
                      <InputText
                        id={field.name}
                        value={field.value || ''}
                        onChange={(e) => {
                          const formatted = formatPhoneNumber(e.target.value, countryCode);
                          handleFieldChange('phone', formatted, field.onChange);
                        }}
                        onBlur={field.onBlur}
                        placeholder={t('placeholders.phone')}
                        className={`flex-1 text-[#696F79] dark:text-gray-300 dark:bg-gray-800 dark:border-gray-600 p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                      />
                    );
                  }}
                />
              </div>
              
              {/* Error Messages */}
              {getFormErrorMessage('countryCode', errors)}
              {getFormErrorMessage('phone', errors)}
              
              {/* Helper Text */}
              <small className="text-[#696F79] dark:text-gray-400 mt-1 block">

              </small>
            </div>
          </div>

          {/* Email Field */}
          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79] dark:text-gray-300">{t('inputs.email')}</label>
              <span className="p-float-label mt-2">
                <Controller name="email" control={control}
                  rules={{
                    required: t('messages.required'),
                    pattern: {
                      value: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                      message: t('messages.email'),
                    },
                    maxLength: {
                      value: 40,
                      message: t('messages.max_length', { count: 40 })
                    }
                  }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.email')}
                      maxLength={40}
                      onChange={(e) => handleFieldChange('email', e.target.value, field.onChange)}
                      className={`w-full text-[#696F79] dark:text-gray-300 dark:bg-gray-800 dark:border-gray-600 p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('email', errors)}
            </div>
          </div>

          {/* Password Field */}
          <div className="mb-4 form-password-toggle w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79] dark:text-gray-300" htmlFor="password">{t('inputs.password')}</label>
              <span className="p-float-label mt-2">
                <Controller name="password" control={control}
                  rules={{ required: t('messages.required') }}
                  render={({ field, fieldState }) => (
                    <Password
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.password')}
                      onChange={(e) => handleFieldChange('password', e.target.value, field.onChange)}
                      className={`text-[#696F79] dark:text-gray-300 dark:bg-gray-800 dark:border-gray-600 pass-input w-full ${classNames({ 'p-invalid': fieldState.invalid })}`}
                      toggleMask />
                  )} />
              </span>
              {getFormErrorMessage('password', errors)}
            </div>
          </div>


          {/* Company Name Field */}
          <div className="mb-4 w-full md:w-6/12">
            <div className="field mx-2">
              <label className="form-label mb-2 text-[#696F79] dark:text-gray-300">{t('inputs.company_name')}</label>
              <span className="p-float-label mt-2">
                <Controller name="company" control={control}
                  rules={{
                    required: t('messages.required'),
                    minLength: {
                      value: 3,
                      message: t('Please enter a company name with at least 3 characters.', { count: 3 })
                    },
                    maxLength: {
                      value: 50,
                      message: t('messages.max_length', { count: 32 })
                    }
                  }}
                  render={({ field, fieldState }) => (
                    <InputText
                      id={field.name}
                      {...field}
                      ref={field.ref}
                      placeholder={t('placeholders.company_name')}
                      maxLength={50}
                      onChange={(e) => handleFieldChange('company', e.target.value, field.onChange)}
                      className={`w-full text-[#696F79] dark:text-gray-300 dark:bg-gray-800 dark:border-gray-600 p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                  )} />
              </span>
              {getFormErrorMessage('company', errors)}
            </div>
          </div>

          {/* Company Location Field */}
          <div className="mb-4 w-full">
            <div className="field mx-2">
              <Controller 
                name="companyLocation" 
                control={control}
                rules={{
                  required: t('messages.required'),
                  validate: (value) => {
                    if (!value) {
                      return t('Please select a location on the map', { default: 'Please select a location on the map' });
                    }
                    if (!value.coordinates) {
                      return t('Please select a valid location on the map', { default: 'Please select a valid location on the map' });
                    }
                    if (!value.coordinates.lat || !value.coordinates.lng) {
                      return t('Invalid location coordinates', { default: 'Invalid location coordinates' });
                    }
                    return true;
                  }
                }}
                render={({ field, fieldState }) => (
                  <LocationPicker
                    value={field.value}
                    onChange={(value) => handleFieldChange('companyLocation', value, field.onChange)}
                    error={fieldState.error?.message}
                    label={t('inputs.company_address')}
                    required={true}
                    mode="select"
                  />
                )} 
              />
            </div>
          </div>


          <button
            type="submit"
            className="main-btn w-full mt-8 text-md sm:text-xl dark:bg-blue-600 dark:hover:bg-blue-700 dark:text-white dark:disabled:bg-gray-600 dark:disabled:text-gray-400 dark:disabled:opacity-50 dark:disabled:cursor-not-allowed dark:disabled:transform-none dark:disabled:shadow-none"
            disabled={register.isLoading}
            onClick={async (e) => {
              // Trigger validation for all fields before submission
              const isValid = await trigger();
              if (!isValid) {
                e.preventDefault();
                toast.current.show({
                  severity: 'error',
                  summary: 'Validation Error',
                  detail: 'Please fill in all required fields correctly',
                  life: 3000
                });
              }
            }}
          >
            {register.isLoading ? (
              <>
                <span className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white dark:border-gray-300 mr-2"></span>
                {t('registering', { default: 'Registering...' })}
              </>
            ) : (
              t('register')
            )}
          </button>
        </form>

        <p className="mt-3 fs-8 text-[#696F79] dark:text-gray-400 text-sm">
          {t('go_to.login')}
          <Link to="/login">
            <span className="mx-1 capitalize text-[#427bf0] dark:text-blue-400">{t('login')}</span>
          </Link>
        </p>
      </div>
    </div>
  )
}

export default Registration;