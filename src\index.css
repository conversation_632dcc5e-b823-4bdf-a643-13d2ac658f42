/* Location Picker Styles */
@import './assets/css/location-picker.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --main_color: #00c3ac;
  --main_color_hover: #02aa96;
  --gray: #dcdcdc;
  
  /* Light Theme Variables */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --surface: #ffffff;
  --surface-hover: #f8fafc;
  --border: #e2e8f0;
  --border-hover: #cbd5e1;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --accent: #3b82f6;
  --accent-hover: #2563eb;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.15);
}

/* Dark Theme Variables */
.dark {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --surface: #1e293b;
  --surface-hover: #334155;
  --border: #334155;
  --border-hover: #475569;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --accent: #3b82f6;
  --accent-hover: #2563eb;
  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-lg: rgba(0, 0, 0, 0.4);
}
body * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.main-color {
  background: var(--main_color);
}

.main-btn {
  color: white;
  border-radius: 6px;
  padding: 6px 10px;
  background-color: var(--main_color);
  border-color: var(--main_color);
  font-weight: bold;
  transition: all 0.5s;
}

.main-btn:hover {
  background-color: var(--main_color_hover);
}

.gray-btn {
  color: rgb(93, 93, 93);
  font-weight: bold;
  border-radius: 6px;
  padding: 6px 10px;
  background-color: var(--gray);
  border-color: var(--gray);
}

.auth-input {
  padding: 23px 30px;
  border-radius: 6px;
  border: 1px solid #8692a6;
  background-color: white !important;
}

.pass-input input {
  width: 100%;
}

.side-image-container {
  background-image: url("./assets/images/auth.jfif");
  background-position: center;
  background-size: cover;
}

.active_tab {
  font-weight: bold;
  background-color: #7de1d55e;
  border-radius: 10px;
}

.filter-field {
  border-right: 2px solid #d7d7d7;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active-bh {
  background-color: #7979795e;
}

/* Remove border from the TabMenu container */
.p-tabmenu.types-tab-menu {
  border: none;
}

/* Remove borders from individual tabs */
.p-tabmenu.types-tab-menu .p-tabmenu-nav {
  border: none;
}

/* Remove borders from tab items */
.p-tabmenu.types-tab-menu .p-tabmenuitem {
  border: none;
  box-shadow: none; /* Remove any shadows if present */
}

.p-tabmenu.types-tab-menu .p-tabmenuitem {
  border-color: var(--main_color_hover) !important;
}

/* Style the active tab */
.p-tabmenu.types-tab-menu .p-highlight * {
  color: var(--main_color);
}

.p-tabmenu.types-tab-menu:hover * {
  color: var(--main_color_hover);
}
/* Remove hover border effect */
.p-tabmenu.types-tab-menu .p-tabmenuitem:hover {
  border: none;
  background-color: #f0f0f0;
}

.p-selectbutton .p-button.p-highlight {
  background: var(--main_color_hover);
  border-color:var(--main_color_hover);
  color: #ffffff;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.3;
  }
}


.sticky-header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table-responsive {
  overflow-y: auto;
  height: calc(100vh - 300px);
}

body {
  overflow-x: hidden;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.sticky-header {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 50;
}

.sticky-header thead th {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 10;
  box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1);
}

/* Responsive DataTable Styles */
.p-datatable-responsive .p-datatable-tbody > tr > td .p-column-title {
  display: none;
}

@media screen and (max-width: 768px) {
  .p-datatable.p-datatable-responsive .p-datatable-thead > tr > th,
  .p-datatable.p-datatable-responsive .p-datatable-tfoot > tr > td {
    display: none !important;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td {
    text-align: left;
    display: block;
    width: 100%;
    float: left;
    clear: left;
    border: 0 none;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td .p-column-title {
    padding: 0.4rem;
    min-width: 30%;
    display: inline-block;
    margin: -0.4em 1em -0.4em -0.4rem;
    font-weight: bold;
  }

  .p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td:last-child {
    border-bottom: 1px solid var(--surface-d);
  }
}

/* Responsive utilities */
.responsive-container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}



.professional-library-section .image-container {
  gap: 8px;
  padding: 8px; 
}


.professional-library-section .image-item:hover {
  opacity: 0.7; 
  transition: opacity 0.2s ease;
}

.react-tel-input .flag-dropdown {
  z-index: 2;
  border-radius: 1rem 0 0 1rem !important;
  border-right: none !important;
  box-shadow: none !important;
}
.react-tel-input .form-control {
  border-left: 2px solid #d1d5db !important;
  border-radius: 0 1rem 1rem 0 !important;
  padding-left: 80px !important;
  position: relative;
}
.react-tel-input .form-control::before {
  content: '';
  position: absolute;
  left: 60px;
  top: 50%;
  transform: translateY(-50%);
  height: 28px;
  width: 2px;
  background: #d1d5db;
  border-radius: 2px;
  z-index: 2;
  display: block;
}
.react-tel-input .country-list {
  border-radius: 1rem !important;
  box-shadow: 0 8px 32px rgba(66,123,240,0.18) !important;
  border: 2px solid #d1d5db !important;
  background: #fff !important;
  font-size: 1.08rem !important;
  padding: 8px 0 !important;
  max-height: 350px !important;
  overflow-y: auto !important;
  z-index: 50 !important;
}
.react-tel-input .country-list .search {
  margin: 8px 16px !important;
  padding: 8px 12px !important;
  border-radius: 0.75rem !important;
  border: 1.5px solid #d1d5db !important;
  font-size: 1rem !important;
  background: #f9fafb !important;
  color: #222 !important;
  box-shadow: none !important;
  width: calc(100% - 32px) !important;
  display: block !important;
}
.react-tel-input .country-list .country {
  padding: 10px 18px !important;
  border-radius: 8px !important;
  margin: 2px 8px !important;
  display: flex;
  align-items: center;
  transition: background 0.18s;
  font-weight: 500;
  font-size: 1.08rem;
}
.react-tel-input .country-list .country:hover, .react-tel-input .country-list .country.highlight {
  background: #f0f6ff !important;
  color: #427bf0 !important;
}
.react-tel-input .country-list .country .country-flag {
  width: 28px !important;
  height: 20px !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 4px rgba(66,123,240,0.10);
  margin-right: 14px !important;
  object-fit: cover;
  border: 1px solid #e5e7eb;
  background: #fff;
  display: inline-block;
}
.react-tel-input .country-list .country .country-name {
  font-size: 1.08rem !important;
  font-weight: 500;
  color: #2d3748;
}
.react-tel-input .country-list .country .dial-code {
  color: #6b7280;
  font-size: 1rem;
  margin-left: auto;
  font-weight: 400;
}




.text-assistant {
  pointer-events: auto !important;
}

.text-assistant * {
  pointer-events: auto !important;
}

.text-assistant .p-button {
  pointer-events: auto !important;
}

.text-assistant .p-dropdown {
  pointer-events: auto !important;
}

.text-assistant .p-slider {
  pointer-events: auto !important;
}

.text-assistant input[type="color"] {
  pointer-events: auto !important;
}

.text-assistant .p-inputtext {
  pointer-events: auto !important;
}

.text-assistant .p-button.p-button-primary {
  background-color: #4338ca !important;
  border-color: #4338ca !important;
  color: white !important;
}

.text-assistant .p-button.p-button-primary:hover {
  background-color: #3730a3 !important;
  border-color: #3730a3 !important;
}

.text-assistant .p-button.p-button-outlined {
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined:hover {
  background-color: rgba(67, 56, 202, 0.1) !important;
}

.text-assistant .p-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

.text-assistant input[type="color"] {
  border: 2px solid #e5e7eb !important;
  border-radius: 4px !important;
  cursor: pointer !important;
}

.text-assistant input[type="color"]:hover {
  border-color: #d1d5db !important;
}

/* تحسين مظهر القوائم المنسدلة */
.text-assistant .p-dropdown {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.text-assistant .p-dropdown:hover {
  border-color: #9ca3af !important;
}

/* تحسين مظهر حقول الإدخال */
.text-assistant .p-inputtext {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.text-assistant .p-inputtext:focus {
  border-color: #4338ca !important;
  box-shadow: 0 0 0 3px rgba(67, 56, 202, 0.1) !important;
}

/* تحسين مظهر الشرائح */
.text-assistant .p-slider {
  margin: 0.5rem 0 !important;
}

.text-assistant .p-slider .p-slider-handle {
  background-color: #4338ca !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.text-assistant .p-slider .p-slider-range {
  background-color: #4338ca !important;
}

/* تحسين مظهر منطقة المعاينة */
.text-assistant .preview-area {
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* تحسين مظهر التعليمات */
.text-assistant .instructions-panel {
  background-color: #eff6ff !important;
  border: 1px solid #bfdbfe !important;
  border-radius: 8px !important;
}

/* تحسين مظهر العناوين */
.text-assistant h4 {
  color: #374151 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
}

/* تحسين مظهر التسميات */
.text-assistant label {
  color: #4b5563 !important;
  font-weight: 500 !important;
  margin-bottom: 0.25rem !important;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* ضمان عمل الخطوط العربية */
@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط الإنجليزية */
@font-face {
  font-family: 'Roboto';
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Playfair Display';
  src: url('https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qC0s.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Pacifico';
  src: url('https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6MmBp0u-.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bebas Neue';
  src: url('https://fonts.gstatic.com/s/bebasneue/v9/JTUSjIg69CK48gW7PXoo9WlhI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dancing Script';
  src: url('https://fonts.gstatic.com/s/dancingscript/v24/If2cXTr6YS-zF4S-kcSWSVi_sxjsohD9F50Ruu7BMSo3ROp6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Orbitron';
  src: url('https://fonts.gstatic.com/s/orbitron/v25/yMJMMIlzdpvBhQQL_SC3X9yhF25-T1nyGy6BoWgz.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lobster';
  src: url('https://fonts.gstatic.com/s/lobster/v28/neILzCirqoswsqX9zoKmNg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abril Fatface';
  src: url('https://fonts.gstatic.com/s/abrilfatface/v12/zOL64pLDpnLkHmFHMKNTQ4g5bs3t6GqJKxeA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Righteous';
  src: url('https://fonts.gstatic.com/s/righteous/v9/1cXxaUPXBpj2rGoU7C9WiHGA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Permanent Marker';
  src: url('https://fonts.gstatic.com/s/permanentmarker/v10/Fh4uPib6I9yqygr9j2ePTWi4QKqyC6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Fredoka One';
  src: url('https://fonts.gstatic.com/s/fredokaone/v8/k3kUo8kEI-tA1RRcTZGmTlHGCaI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bangers';
  src: url('https://fonts.gstatic.com/s/bangers/v12/FeVQS0BTqb0h60ACH55Q2J5hm.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Chewy';
  src: url('https://fonts.gstatic.com/s/chewy/v12/uK_94ruUb-k-wn52KjI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kaushan Script';
  src: url('https://fonts.gstatic.com/s/kaushanscript/v14/vm8vdRfvXFLG3OLnsO15WYS5DG74wNc.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Satisfy';
  src: url('https://fonts.gstatic.com/s/satisfy/v12/rP2Hp2yn6lkG50LoCZOIHQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Great Vibes';
  src: url('https://fonts.gstatic.com/s/greatvibes/v12/RWmMoLWRv4ITMsfS8c0tPvQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cinzel';
  src: url('https://fonts.gstatic.com/s/cinzel/v16/8vIU7ww63mVu7gtR-kwKxNvkNOjw-tbnTYrvTO5c4A.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'UnifrakturMaguntia';
  src: url('https://fonts.gstatic.com/s/unifrakturmaguntia/v12/WWXPlieVYwiGNomYU-ciRLRvEmK7oaVem2ZI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Creepster';
  src: url('https://fonts.gstatic.com/s/creepster/v12/AlZy_zVUqJz4yMrniH4Rcn35.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Faster One';
  src: url('https://fonts.gstatic.com/s/fasterone/v12/H4ciBXCHmdfClFd-vWhxXPX5.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Press Start 2P';
  src: url('https://fonts.gstatic.com/s/pressstart2p/v14/e3t4euO8T-267oIAQAu6jDQyK3nVivM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'VT323';
  src: url('https://fonts.gstatic.com/s/vt323/v12/pxiKyp0ihIEF2isfFJU.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Share Tech Mono';
  src: url('https://fonts.gstatic.com/s/sharetechmono/v10/J7aHnp1uDWRBEqV98dVQztYldFcLowEF.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Space Mono';
  src: url('https://fonts.gstatic.com/s/spacemono/v10/i7dPIFZifjKcF5UAWdDRYEF8RQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Major Mono Display';
  src: url('https://fonts.gstatic.com/s/majormonodisplay/v6/RWmVoLyb5fEqtsfBX9PDZIGr2tFubRhLCn2QIndPww.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Crimson Text';
  src: url('https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJfbwhT.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Libre Baskerville';
  src: url('https://fonts.gstatic.com/s/librebaskerville/v14/kmKnZrc3Hgbbcjq75U4uslyuy4kn0qNZaxY.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lora';
  src: url('https://fonts.gstatic.com/s/lora/v26/0QI6MX1D_JOuGQbT0gvTJPa787weuyJGmKM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('https://fonts.gstatic.com/s/sourcesanspro/v21/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('https://fonts.gstatic.com/s/nunito/v24/XRXI3I6Li01BKofiOc5wtlZ2di8HDLshdTQ3jw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Quicksand';
  src: url('https://fonts.gstatic.com/s/quicksand/v29/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-xw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Comfortaa';
  src: url('https://fonts.gstatic.com/s/comfortaa/v37/1Pt_g8LJRfWJmhDAuUsSQamb1W0lwk4S4TbMDrMfJQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Varela Round';
  src: url('https://fonts.gstatic.com/s/varelaround/v13/w8gdH283Tvk__Lua32TysjIfp8uPLdshZg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Maven Pro';
  src: url('https://fonts.gstatic.com/s/mavenpro/v32/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8SX25nCpkp4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Exo 2';
  src: url('https://fonts.gstatic.com/s/exo2/v20/7cH1v4okm5zmbvwkAx_sfcEuiD8jWfWcPg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rajdhani';
  src: url('https://fonts.gstatic.com/s/rajdhani/v15/LDI2apCSOBg7S-QT7paQc6M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Titillium Web';
  src: url('https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmCA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Josefin Sans';
  src: url('https://fonts.gstatic.com/s/josefinsans/v25/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQbMZhKg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abel';
  src: url('https://fonts.gstatic.com/s/abel/v18/MwQ5bhbm2POE6VhLPw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anton';
  src: url('https://fonts.gstatic.com/s/anton/v23/1Ptgg87LROyAm3Kz-Co.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bungee';
  src: url('https://fonts.gstatic.com/s/bungee/v6/N0bU2SZBIuF2PU_0AnR1Gd8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Black Ops One';
  src: url('https://fonts.gstatic.com/s/blackopsone/v12/qWcsB6-ypo7xBdr6Xshe96H3aDbbtwkh.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Russo One';
  src: url('https://fonts.gstatic.com/s/russoone/v14/Z9XUDmZRWg6M1LvRYsHOz8mJ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Audiowide';
  src: url('https://fonts.gstatic.com/s/audiowide/v8/l7gdbjpo0cum0ckerWCtkQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa One';
  src: url('https://fonts.gstatic.com/s/changaone/v13/xfu00W3wXn3QLUJXhzq46AbouLfbK64.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Sniglet';
  src: url('https://fonts.gstatic.com/s/sniglet/v13/cIf9MaFLtkE2UupGgQYhiA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Boogaloo';
  src: url('https://fonts.gstatic.com/s/boogaloo/v12/kmK-Zq45GAvOdnaW6x1F_SrQo.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bubblegum Sans';
  src: url('https://fonts.gstatic.com/s/bubblegumsans/v12/AYCSpXb_Z9EORv1M5QTjEzMEtdaHzoPPbqR4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cherry Cream Soda';
  src: url('https://fonts.gstatic.com/s/cherrycreamsoda/v12/UMBIrOxBrW6w2FFyi9paG0fdVdRciQd6A4Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Freckle Face';
  src: url('https://fonts.gstatic.com/s/freckleface/v9/AMOWz4SXrmKHCvXTohxY-YI0Uw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gloria Hallelujah';
  src: url('https://fonts.gstatic.com/s/gloriahallelujah/v12/LYjYdHv3pUkNBMypJh7elzXKCM41xtdECq76mk.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Indie Flower';
  src: url('https://fonts.gstatic.com/s/indieflower/v17/m8JVjfNVeKWVnh3QMuKkFcZVaUuC.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kalam';
  src: url('https://fonts.gstatic.com/s/kalam/v11/YA9Qr0Wd4kDdMtD6GgLLmCUItqGt.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Patrick Hand';
  src: url('https://fonts.gstatic.com/s/patrickhand/v14/Ln1FzOA-y6TkwHrOUc6NnUjKRp9m.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Reenie Beanie';
  src: url('https://fonts.gstatic.com/s/reeniebeanie/v11/z7NSdR76eDkaJKZJFkkjuvWxXPq1qw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rock Salt';
  src: url('https://fonts.gstatic.com/s/rocksalt/v11/MwQ0bhv11fDH6wL6ZCL4I8Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Shadows Into Light';
  src: url('https://fonts.gstatic.com/s/shadowsintolight/v12/UqyNK9UOIntux_czAvDQx_ZcHqZXBNQDcg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Special Elite';
  src: url('https://fonts.gstatic.com/s/specialelite/v11/XLYgIZbkc4JPUL5CVArUVL0ntnAOTQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Walter Turncoat';
  src: url('https://fonts.gstatic.com/s/walterturncoat/v12/snfys0Gs98ln43n0d-14ULoToe67YB2M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط العربية الجديدة */
@font-face {
  font-family: 'Scheherazade New';
  src: url('https://fonts.gstatic.com/s/scheherazadenew/v4/4UaBrE6tmq0gO-tVs9Ipr5-9-mbRvNUF2I.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lateef';
  src: url('https://fonts.gstatic.com/s/lateef/v17/hESw6XVnNCxEvkbMpheEZo_H_w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Harmattan';
  src: url('https://fonts.gstatic.com/s/harmattan/v8/gokpH6L2DkFvVvRp9XpTS0CjkP1Yog.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'El Messiri';
  src: url('https://fonts.gstatic.com/s/elmessiri/v12/K2F0fZBRmr9vQ1pHEey6GIGo8_pv3myYjuXCe65ghj3OTw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Aref Ruqaa';
  src: url('https://fonts.gstatic.com/s/arefruqaa/v12/WwkbxPW1E165DjQ5VsZzqN5NjF7Nw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Readex Pro';
  src: url('https://fonts.gstatic.com/s/readexpro/v1/SLXYc1bJ7HE5YDoGPuzj_dh8na74Kiw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Sans Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexsansarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Arabic';
  src: url('https://fonts.gstatic.com/s/notosansarabic/v18/nwpCt6W9KfF6gV1yPu9T3JqRBNbE8tq1lx20.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Arabic';
  src: url('https://fonts.gstatic.com/s/notoserifarabic/v18/ga6Iaw1J5X9T9RW6j9bNVlsKbJovrb0b8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alkalami';
  src: url('https://fonts.gstatic.com/s/alkalami/v1/zOL-4pbPn6Im26Ke4HOxT7Y.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anek Arabic';
  src: url('https://fonts.gstatic.com/s/anekarabic/v1/5aUz9_-1phKLFgshYDvh6O4hp3wSa0b4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexserifarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Nastaliq Urdu';
  src: url('https://fonts.gstatic.com/s/notonastaliqurdu/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Rashi Hebrew';
  src: url('https://fonts.gstatic.com/s/notorashihebrew/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Hebrew';
  src: url('https://fonts.gstatic.com/s/notosanshebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Hebrew';
  src: url('https://fonts.gstatic.com/s/notoserifhebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Devanagari';
  src: url('https://fonts.gstatic.com/s/notosansdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Devanagari';
  src: url('https://fonts.gstatic.com/s/notoserifdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Bengali';
  src: url('https://fonts.gstatic.com/s/notosansbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Bengali';
  src: url('https://fonts.gstatic.com/s/notoserifbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Thai';
  src: url('https://fonts.gstatic.com/s/notosansthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Thai';
  src: url('https://fonts.gstatic.com/s/notoserifthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Korean';
  src: url('https://fonts.gstatic.com/s/notosanskorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Korean';
  src: url('https://fonts.gstatic.com/s/notoserifkorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Japanese';
  src: url('https://fonts.gstatic.com/s/notosansjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Japanese';
  src: url('https://fonts.gstatic.com/s/notoserifjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* تحسين أداء قائمة الخطوط */
.font-dropdown-panel {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.font-dropdown-panel .p-dropdown-items {
  max-height: 250px !important;
}

.font-dropdown-panel .p-dropdown-item {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  border-bottom: 1px solid #f3f4f6 !important;
}

.font-dropdown-panel .p-dropdown-item:hover {
  background-color: #f9fafb !important;
}

.font-dropdown-panel .p-dropdown-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* تحسين مظهر البحث في قائمة الخطوط */
.font-dropdown-panel .p-dropdown-filter {
  padding: 0.5rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  margin-bottom: 0.5rem !important;
}

.font-dropdown-panel .p-dropdown-filter-input {
  width: 100% !important;
  padding: 0.5rem !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
}

/* تحسين أداء Virtual Scrolling */
.font-dropdown-panel .p-virtualscroller {
  max-height: 200px !important;
}

.font-dropdown-panel .p-virtualscroller-content {
  padding: 0 !important;
}

/* تحسين مظهر مثال الخط */
.font-dropdown-panel .p-dropdown-item span:last-child {
  font-size: 0.75rem !important;
  opacity: 0.7 !important;
  margin-left: 0.5rem !important;
}

/* تحسين أداء التحميل */
.text-assistant .p-dropdown.p-component {
  transition: all 0.2s ease !important;
}

.text-assistant .p-dropdown.p-component:not(.p-disabled):hover {
  border-color: #3b82f6 !important;
}

.text-assistant .p-dropdown.p-component.p-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Dark Mode Styles */
.dark {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.dark .sticky-header {
  background-color: var(--surface);
  box-shadow: 0 2px 4px var(--shadow);
}

.dark .sticky-header thead th {
  background: var(--surface);
}

.dark .active_tab {
  background-color: rgba(59, 130, 246, 0.2);
  color: var(--accent);
}

.dark .filter-field {
  border-right-color: var(--border);
}

.dark .active-bh {
  background-color: rgba(148, 163, 184, 0.2);
}

.dark .auth-input {
  background-color: var(--surface) !important;
  border-color: var(--border);
  color: var(--text-primary);
}

.dark .main-btn {
  background-color: var(--accent);
  border-color: var(--accent);
}

.dark .main-btn:hover {
  background-color: var(--accent-hover);
  border-color: var(--accent-hover);
}

.dark .gray-btn {
  background-color: var(--bg-tertiary);
  border-color: var(--border);
  color: var(--text-secondary);
}

/* Dark Mode for PrimeReact Components */
.dark .p-datatable {
  background-color: var(--surface);
  color: var(--text-primary);
}

.dark .p-datatable .p-datatable-header {
  background-color: var(--bg-secondary);
  border-color: var(--border);
}

.dark .p-datatable .p-datatable-thead > tr > th {
  background-color: var(--bg-secondary);
  border-color: var(--border);
  color: var(--text-primary);
}

.dark .p-datatable .p-datatable-tbody > tr {
  background-color: var(--surface);
  border-color: var(--border);
}

.dark .p-datatable .p-datatable-tbody > tr:hover {
  background-color: var(--surface-hover);
}

.dark .p-datatable .p-datatable-tbody > tr > td {
  border-color: var(--border);
  color: var(--text-primary);
}

.dark .p-button {
  background-color: var(--accent);
  border-color: var(--accent);
  color: white;
}

.dark .p-button:hover {
  background-color: var(--accent-hover);
  border-color: var(--accent-hover);
}

.dark .p-button.p-button-outlined {
  background-color: transparent;
  color: var(--accent);
  border-color: var(--accent);
}

.dark .p-button.p-button-outlined:hover {
  background-color: var(--accent);
  color: white;
}

.dark .p-inputtext {
  background-color: var(--surface);
  border-color: var(--border);
  color: var(--text-primary);
}

.dark .p-inputtext:focus {
  border-color: var(--accent);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .p-dropdown {
  background-color: var(--surface);
  border-color: var(--border);
  color: var(--text-primary);
}

.dark .p-dropdown-panel {
  background-color: var(--surface);
  border-color: var(--border);
  box-shadow: 0 4px 6px var(--shadow-lg);
}

.dark .p-dropdown-item {
  color: var(--text-primary);
}

.dark .p-dropdown-item:hover {
  background-color: var(--surface-hover);
}

.dark .p-dropdown-item.p-highlight {
  background-color: var(--accent);
  color: white;
}

/* Dark Mode for Banner Menu */
.dark .p-menu {
  background-color: var(--surface);
  border-color: var(--border);
  box-shadow: 0 4px 6px var(--shadow-lg);
}

.dark .p-menu .p-menuitem-link {
  color: var(--text-primary);
  background-color: transparent;
}

.dark .p-menu .p-menuitem-link:hover {
  background-color: var(--surface-hover);
  color: var(--accent);
}

/* Dark Mode for DataTable Headers and Footers */
.dark .p-datatable {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-datatable .p-datatable-header {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-datatable .p-datatable-footer {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-datatable .p-datatable-thead > tr > th {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-datatable .p-datatable-tbody > tr {
  background-color: var(--bg-primary) !important;
  border-color: var(--border) !important;
}

.dark .p-datatable .p-datatable-tbody > tr:nth-child(even) {
  background-color: var(--bg-secondary) !important;
}

.dark .p-datatable .p-datatable-tbody > tr:hover {
  background-color: var(--surface-hover) !important;
}

.dark .p-datatable .p-datatable-tbody > tr > td {
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Dark Mode for DataTable Borders and Pagination */
.dark .p-datatable .p-datatable-table {
  border-color: var(--border) !important;
}

.dark .p-datatable .p-datatable-tbody > tr:last-child > td {
  border-bottom-color: var(--border) !important;
}

.dark .p-datatable .p-datatable-tbody > tr > td:first-child {
  border-left-color: var(--border) !important;
}

.dark .p-datatable .p-datatable-tbody > tr > td:last-child {
  border-right-color: var(--border) !important;
}

/* Dark Mode for Pagination Controls */
.dark .p-paginator {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-paginator .p-paginator-page {
  background-color: transparent !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-paginator .p-paginator-page:hover {
  background-color: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
}

.dark .p-paginator .p-paginator-page.p-highlight {
  background-color: var(--accent) !important;
  border-color: var(--accent) !important;
  color: white !important;
}

.dark .p-paginator .p-paginator-first,
.dark .p-paginator .p-paginator-prev,
.dark .p-paginator .p-paginator-next,
.dark .p-paginator .p-paginator-last {
  background-color: transparent !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-paginator .p-paginator-first:hover,
.dark .p-paginator .p-paginator-prev:hover,
.dark .p-paginator .p-paginator-next:hover,
.dark .p-paginator .p-paginator-last:hover {
  background-color: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
}

/* Dark Mode for Events Page */
.dark .events-header {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .events-filters {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  box-shadow: 0 1px 3px var(--shadow) !important;
}

.dark .events-table-container {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  box-shadow: 0 1px 3px var(--shadow) !important;
}

.dark .events-mobile-card {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .events-action-dropdown {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  box-shadow: 0 10px 25px var(--shadow-lg) !important;
}

/* Dark Mode for Events Page General Elements */
.dark .text-gray-800 {
  color: var(--text-primary) !important;
}

.dark .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark .text-gray-500 {
  color: var(--text-tertiary) !important;
}

.dark .border-gray-200 {
  border-color: var(--border) !important;
}

.dark .bg-white {
  background-color: var(--surface) !important;
}

.dark .hover\:bg-gray-50:hover {
  background-color: var(--surface-hover) !important;
}

/* Dark Mode for Dashboard Containers */
.dark .dashboard-white-container {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .dashboard-white-container:hover {
  background-color: var(--surface-hover) !important;
  box-shadow: 0 10px 25px var(--shadow-lg) !important;
}

/* Dark Mode for Dashboard Stats Cards */
.dark .dashboard-white-container .text-gray-500 {
  color: var(--text-secondary) !important;
}

.dark .dashboard-white-container .text-gray-700 {
  color: var(--text-primary) !important;
}

.dark .dashboard-white-container .text-gray-400 {
  color: var(--text-tertiary) !important;
}

/* Dark Mode for Dashboard Card Lists */
.dark .dashboard-white-container .border-gray-100 {
  border-color: var(--border) !important;
}

.dark .dashboard-white-container .bg-gray-50 {
  background-color: var(--bg-secondary) !important;
}

.dark .dashboard-white-container .hover\:bg-gray-100:hover {
  background-color: var(--surface-hover) !important;
}

/* Dark Mode for Dashboard Progress Bars */
.dark .dashboard-white-container .bg-gray-100 {
  background-color: var(--bg-tertiary) !important;
}

.dark .dashboard-white-container .bg-gray-200 {
  background-color: var(--border) !important;
}

/* Dark Mode for Dashboard Specific Elements */
.dark .dashboard-white-container .bg-green-50 {
  background-color: rgba(34, 197, 94, 0.1) !important;
}

.dark .dashboard-white-container .bg-orange-50 {
  background-color: rgba(249, 115, 22, 0.1) !important;
}

.dark .dashboard-white-container .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

.dark .dashboard-white-container .bg-purple-50 {
  background-color: rgba(147, 51, 234, 0.1) !important;
}

.dark .dashboard-white-container .bg-amber-50 {
  background-color: rgba(245, 158, 11, 0.1) !important;
}

/* Dark Mode for Dashboard Table Elements */
.dark .dashboard-white-container .bg-gray-50 {
  background-color: var(--bg-secondary) !important;
}

.dark .dashboard-white-container .divide-gray-200 {
  border-color: var(--border) !important;
}

.dark .dashboard-white-container .hover\:bg-gray-50:hover {
  background-color: var(--surface-hover) !important;
}

/* Dark Mode for Groups Cards Overview */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 {
  background: linear-gradient(to right, var(--bg-secondary), var(--bg-tertiary), var(--bg-secondary)) !important;
  border-color: var(--border) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .text-gray-800 {
  color: var(--text-primary) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .text-gray-500 {
  color: var(--text-tertiary) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .border-blue-200 {
  border-color: var(--border) !important;
}

/* Dark Mode for Groups Cards Overview Cards */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-white {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-white:hover {
  background-color: var(--surface-hover) !important;
  box-shadow: 0 10px 25px var(--shadow-lg) !important;
}

/* Dark Mode for Groups Cards Overview Table */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-gray-50 {
  background-color: var(--bg-secondary) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .divide-gray-200 {
  border-color: var(--border) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .hover\:bg-gray-50:hover {
  background-color: var(--surface-hover) !important;
}

/* Dark Mode for Groups Cards Overview Summary Cards */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-green-50 {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-purple-50 {
  background-color: rgba(147, 51, 234, 0.1) !important;
  border-color: rgba(147, 51, 234, 0.2) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-orange-50 {
  background-color: rgba(249, 115, 22, 0.1) !important;
  border-color: rgba(249, 115, 22, 0.2) !important;
}

/* Dark Mode for Groups Cards Overview Progress Bars */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-gray-200 {
  background-color: var(--border) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-gray-100 {
  background-color: var(--bg-tertiary) !important;
}

/* Dark Mode for Groups Cards Overview Toggle Button */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-gradient-to-r.from-gray-50.to-gray-100 {
  background: linear-gradient(to right, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-color: var(--border) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .hover\:from-blue-50.hover\:to-indigo-50:hover {
  background: linear-gradient(to right, var(--surface-hover), var(--surface)) !important;
  border-color: var(--accent) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .hover\:border-blue-300:hover {
  border-color: var(--accent) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .group-hover\:text-blue-600:hover {
  color: var(--accent) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .group-hover\:opacity-10:hover {
  background: linear-gradient(to right, var(--accent), var(--accent-hover)) !important;
  opacity: 0.1 !important;
}

/* Dark Mode for Groups Cards Overview Table Row Hover */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .hover\:bg-gray-50:hover {
  background-color: var(--surface-hover) !important;
}

/* Dark Mode for Groups Cards Overview Table Headers */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-gray-50 {
  background-color: var(--bg-secondary) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .text-gray-500 {
  color: var(--text-tertiary) !important;
}

/* Dark Mode for Groups Cards Overview Table Body */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .bg-white {
  background-color: var(--surface) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .text-gray-500 {
  color: var(--text-tertiary) !important;
}

/* Dark Mode for Groups Cards Overview - Remove Hover from Detailed Table */
.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 .hover\:bg-gray-50:hover {
  background-color: transparent !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 tbody tr:hover {
  background-color: transparent !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 tbody tr:hover td {
  background-color: transparent !important;
}

.dark .bg-gradient-to-r.from-blue-50.via-indigo-50.to-purple-50 tbody tr:hover th {
  background-color: transparent !important;
}

/* Dark Mode for Groups DataTable */
.dark .groups-header-container {
  background: linear-gradient(to right, var(--bg-secondary), var(--bg-tertiary), var(--bg-secondary)) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .groups-header-container .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark .groups-header-container .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .groups-header-container .text-gray-500 {
  color: var(--text-primary) !important;
}

.dark .groups-header-container .bg-white\/90 {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .groups-header-container .bg-white\/80 {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .groups-header-container .border-gray-200 {
  border-color: var(--border) !important;
}

.dark .groups-header-container .hover\:bg-white:hover {
  background-color: var(--surface-hover) !important;
}

.dark .groups-header-container .hover\:border-gray-300:hover {
  border-color: var(--border-hover) !important;
}

.dark .groups-header-container .text-gray-700 {
  color: var(--text-primary) !important;
}

.dark .groups-header-container .text-gray-400 {
  color: var(--text-secondary) !important;
}

.dark .groups-header-container .text-gray-300 {
  color: var(--text-primary) !important;
}

.dark .groups-header-container .text-gray-800 {
  color: var(--text-primary) !important;
}

/* Dark Mode for Groups Cards */
.dark .groups-card {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .groups-card:hover {
  background-color: var(--surface-hover) !important;
  box-shadow: 0 10px 25px var(--shadow-lg) !important;
}

.dark .groups-card .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark .groups-card .text-gray-500 {
  color: var(--text-secondary) !important;
}

.dark .groups-card .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .groups-card .border-gray-200 {
  border-color: var(--border) !important;
}

.dark .groups-card .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .groups-card .bg-green-50 {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .groups-card .text-blue-700 {
  color: var(--accent) !important;
}

.dark .groups-card .text-green-700 {
  color: #22c55e !important;
}

.dark .groups-card .text-blue-600 {
  color: var(--accent) !important;
}

.dark .groups-card .text-green-600 {
  color: #22c55e !important;
}

/* Dark Mode for Groups Buttons */
.dark .groups-card .bg-blue-50:hover {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .groups-card .bg-green-50:hover {
  background-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .groups-card .bg-red-50 {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-color: rgba(239, 68, 68, 0.2) !important;
}

.dark .groups-card .bg-red-50:hover {
  background-color: rgba(239, 68, 68, 0.2) !important;
}

.dark .groups-card .bg-purple-50 {
  background-color: rgba(147, 51, 234, 0.1) !important;
  border-color: rgba(147, 51, 234, 0.2) !important;
}

.dark .groups-card .bg-purple-50:hover {
  background-color: rgba(147, 51, 234, 0.2) !important;
}

.dark .groups-card .text-red-700 {
  color: #ef4444 !important;
}

.dark .groups-card .text-purple-700 {
  color: #9333ea !important;
}

.dark .groups-card .text-red-600 {
  color: #ef4444 !important;
}

.dark .groups-card .text-purple-600 {
  color: #9333ea !important;
}

/* Dark Mode for Groups Mobile Cards */
.dark .groups-mobile-card {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .groups-mobile-card:hover {
  background-color: var(--surface-hover) !important;
  box-shadow: 0 10px 25px var(--shadow-lg) !important;
}

.dark .groups-mobile-card .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark .groups-mobile-card .text-gray-500 {
  color: var(--text-secondary) !important;
}

.dark .groups-mobile-card .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .groups-mobile-card .border-gray-200 {
  border-color: var(--border) !important;
}

.dark .groups-mobile-card .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .groups-mobile-card .bg-green-50 {
  background-color: rgba(34, 197, 94, 0.1) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .groups-mobile-card .text-blue-700 {
  color: var(--accent) !important;
}

.dark .groups-mobile-card .text-green-700 {
  color: #22c55e !important;
}

.dark .groups-mobile-card .text-blue-600 {
  color: var(--accent) !important;
}

.dark .groups-mobile-card .text-green-600 {
  color: #22c55e !important;
}

/* Dark Mode for Groups Action Menu */
.dark .groups-action-menu {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .groups-action-menu .text-gray-900 {
  color: var(--text-primary) !important;
}

.dark .groups-action-menu .text-gray-500 {
  color: var(--text-secondary) !important;
}

.dark .groups-action-menu .border-gray-100 {
  border-color: var(--border) !important;
}

.dark .groups-action-menu .bg-white {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .groups-action-menu .hover\:bg-blue-50:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

.dark .groups-action-menu .hover\:bg-green-50:hover {
  background-color: rgba(34, 197, 94, 0.1) !important;
}

.dark .groups-action-menu .hover\:bg-purple-50:hover {
  background-color: rgba(147, 51, 234, 0.1) !important;
}

.dark .groups-action-menu .hover\:bg-red-50:hover {
  background-color: rgba(239, 68, 68, 0.1) !important;
}

.dark .groups-action-menu .bg-gray-100 {
  background-color: var(--bg-secondary) !important;
}

.dark .groups-action-menu .hover\:bg-gray-200:hover {
  background-color: var(--surface-hover) !important;
}

.dark .groups-action-menu .text-gray-700 {
  color: var(--text-primary) !important;
}

/* Dark Mode for Groups Cards Management Section */
.dark .groups-cards-management {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .groups-cards-management .text-gray-800 {
  color: var(--text-primary) !important;
}

.dark .groups-cards-management .text-gray-500 {
  color: var(--text-secondary) !important;
}

.dark .groups-cards-management .border-gray-200 {
  border-color: var(--border) !important;
}

.dark .groups-cards-management .bg-gradient-to-r.from-gray-50.to-gray-100 {
  background: linear-gradient(to right, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-color: var(--border) !important;
}

.dark .groups-cards-management .hover\:from-blue-50.hover\:to-indigo-50:hover {
  background: linear-gradient(to right, var(--surface-hover), var(--surface)) !important;
  border-color: var(--accent) !important;
}

.dark .groups-cards-management .hover\:border-blue-300:hover {
  border-color: var(--accent) !important;
}

.dark .groups-cards-management .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .groups-cards-management .group-hover\:text-blue-600:hover {
  color: var(--accent) !important;
}

/* Dark Mode for Groups Available/Assigned Cards - بدون hover */
.dark .groups-cards-management .bg-gradient-to-br.from-green-50.via-emerald-50.to-green-100 {
  background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .groups-cards-management .bg-gradient-to-br.from-blue-50.via-indigo-50.to-blue-100 {
  background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1), rgba(59, 130, 246, 0.1)) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

/* إزالة hover من الحاويات الكبيرة */
.dark .groups-cards-management .bg-gradient-to-br.from-green-50.via-emerald-50.to-green-100:hover {
  background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  box-shadow: none !important;
  transform: none !important;
}

.dark .groups-cards-management .bg-gradient-to-br.from-blue-50.via-indigo-50.to-blue-100:hover {
  background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1), rgba(59, 130, 246, 0.1)) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  box-shadow: none !important;
  transform: none !important;
}

.dark .groups-cards-management .bg-white {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .groups-cards-management .border-green-200 {
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .groups-cards-management .border-blue-200 {
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .groups-cards-management .hover\:border-green-300:hover {
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.dark .groups-cards-management .hover\:border-blue-300:hover {
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.dark .groups-cards-management .text-green-800 {
  color: #22c55e !important;
}

.dark .groups-cards-management .text-blue-800 {
  color: var(--accent) !important;
}

.dark .groups-cards-management .text-green-700 {
  color: #22c55e !important;
}

.dark .groups-cards-management .text-blue-700 {
  color: var(--accent) !important;
}

.dark .groups-cards-management .text-green-600 {
  color: #22c55e !important;
}

.dark .groups-cards-management .text-blue-600 {
  color: var(--accent) !important;
}

.dark .groups-cards-management .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .groups-cards-management .text-gray-500 {
  color: var(--text-tertiary) !important;
}

.dark .groups-cards-management .bg-green-50 {
  background-color: rgba(34, 197, 94, 0.1) !important;
}

.dark .groups-cards-management .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

.dark .groups-cards-management .bg-green-500 {
  background-color: #22c55e !important;
}

.dark .groups-cards-management .bg-blue-500 {
  background-color: var(--accent) !important;
}

/* إصلاح مشكلة الـ hover في Group Cards Management */
.dark .groups-cards-management .hover\:bg-gray-50:hover {
  background-color: var(--surface-hover) !important;
}

.dark .groups-cards-management .hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
}

.dark .groups-cards-management .hover\:border-gray-300:hover {
  border-color: var(--border-hover) !important;
}

.dark .groups-cards-management .hover\:scale-105:hover {
  transform: scale(1.05) !important;
}

.dark .groups-cards-management .hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2) !important;
}



/* Dark Mode for Groups Pagination */
.dark .groups-pagination {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .groups-pagination .border-gray-300 {
  border-color: var(--border) !important;
}

.dark .groups-pagination .bg-white {
  background-color: var(--surface) !important;
}

.dark .groups-pagination .hover\:bg-gray-100:hover {
  background-color: var(--surface-hover) !important;
}

.dark .groups-pagination .text-gray-600 {
  color: var(--text-secondary) !important;
}

/* Dark Mode for Package Cards */
.dark .package-card {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .package-card:hover {
  background-color: var(--surface-hover) !important;
  box-shadow: 0 10px 25px var(--shadow-lg) !important;
}

/* Dark Mode for Badge Elements */
.dark .bg-indigo-100 {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .text-indigo-800 {
  color: rgba(147, 197, 253, 0.9) !important;
}


/* Dark Mode for Templates Dropdown */
.dark .p-dropdown {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-dropdown:hover {
  border-color: var(--border-hover) !important;
}

.dark .p-dropdown:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.dark .p-dropdown-label {
  color: var(--text-primary) !important;
}

.dark .p-dropdown-trigger {
  color: var(--text-secondary) !important;
}

.dark .p-dropdown-trigger:hover {
  background-color: var(--surface-hover) !important;
}

.dark .templates-dropdown-panel {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  box-shadow: 0 10px 25px var(--shadow-lg) !important;
}

.dark .templates-dropdown-panel .p-dropdown-items .p-dropdown-item {
  color: var(--text-primary) !important;
  background-color: transparent !important;
}

.dark .templates-dropdown-panel .p-dropdown-items .p-dropdown-item:hover {
  background-color: var(--surface-hover) !important;
  color: var(--accent) !important;
}

.dark .templates-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
  background-color: var(--accent) !important;
  color: white !important;
}

.dark .p-menu .p-menuitem-text {
  color: var(--text-primary);
}

.dark .p-menu .p-menuitem-icon {
  color: var(--text-secondary);
}

.dark .p-menu .p-menuitem-link:hover .p-menuitem-icon {
  color: var(--accent);
}

.dark .p-tabmenu {
  background-color: var(--surface);
  border-color: var(--border);
}

.dark .p-tabmenu .p-tabmenu-nav {
  background-color: var(--surface);
  border-color: var(--border);
}

.dark .p-tabmenu .p-tabmenuitem {
  background-color: var(--surface);
  border-color: var(--border);
  color: var(--text-primary);
}

.dark .p-tabmenu .p-tabmenuitem:hover {
  background-color: var(--surface-hover);
}

.dark .p-tabmenu .p-tabmenuitem.p-highlight {
  background-color: var(--accent);
  color: white;
}

/* تحسين تصميم الوضع المظلم للـ Dialogs - احترافي جداً */
.dark .p-dialog {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  overflow: hidden !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  overflow: hidden !important;
}

/* إصلاح إضافي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog,
html.dark .p-dialog * {
  color-scheme: dark !important;
}

/* إصلاح خاص للـ modals التي قد لا تطبق الوضع المظلم */
html.dark .p-dialog-mask .p-dialog {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  color: var(--text-primary) !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-content {
  background: var(--surface) !important;
  color: var(--text-primary) !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-footer {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  color: var(--text-primary) !important;
}

/* إصلاح شامل للوضع المظلم في الـ modals - تطبيق على جميع العناصر */
html.dark .p-dialog-mask .p-dialog .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-inputnumber {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

html.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-inputnumber:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: var(--surface) !important;
}

html.dark .p-dialog-mask .p-dialog .p-button {
  background: linear-gradient(135deg, var(--accent), var(--accent-hover)) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border) !important;
}

html.dark .p-dialog-mask .p-dialog label,
html.dark .p-dialog-mask .p-dialog .form-label,
html.dark .p-dialog-mask .p-dialog .p-error,
html.dark .p-dialog-mask .p-dialog small {
  color: var(--text-primary) !important;
}

html.dark .p-dialog-mask .p-dialog .p-error {
  color: #ef4444 !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

/* إصلاح شامل للوضع المظلم في الـ modals - تطبيق مباشر على body */
body.dark .p-dialog-mask .p-dialog,
html.dark .p-dialog-mask .p-dialog {
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.1) !important;
}

body.dark .p-dialog-mask .p-dialog .p-dialog-header,
html.dark .p-dialog-mask .p-dialog .p-dialog-header {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-bottom: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

body.dark .p-dialog-mask .p-dialog .p-dialog-content,
html.dark .p-dialog-mask .p-dialog .p-dialog-content {
  background: #1e293b !important;
  color: #f1f5f9 !important;
}

body.dark .p-dialog-mask .p-dialog .p-dialog-footer,
html.dark .p-dialog-mask .p-dialog .p-dialog-footer {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-top: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

/* إصلاح شامل لجميع عناصر الـ modals */
body.dark .p-dialog-mask .p-dialog .p-inputtext,
body.dark .p-dialog-mask .p-dialog .p-inputtextarea,
body.dark .p-dialog-mask .p-dialog .p-dropdown,
body.dark .p-dialog-mask .p-dialog .p-multiselect,
body.dark .p-dialog-mask .p-dialog .p-calendar,
body.dark .p-dialog-mask .p-dialog .p-inputnumber,
html.dark .p-dialog-mask .p-dialog .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

body.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
body.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
body.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
body.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
body.dark .p-dialog-mask .p-dialog .p-calendar:focus,
body.dark .p-dialog-mask .p-dialog .p-inputnumber:focus,
html.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

body.dark .p-dialog-mask .p-dialog .p-button,
html.dark .p-dialog-mask .p-dialog .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

body.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary,
html.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

body.dark .p-dialog-mask .p-dialog label,
body.dark .p-dialog-mask .p-dialog .form-label,
body.dark .p-dialog-mask .p-dialog small,
html.dark .p-dialog-mask .p-dialog label,
html.dark .p-dialog-mask .p-dialog .form-label,
html.dark .p-dialog-mask .p-dialog small {
  color: #f1f5f9 !important;
}

body.dark .p-dialog-mask .p-dialog .p-error,
html.dark .p-dialog-mask .p-dialog .p-error {
  color: #ef4444 !important;
}

/* إصلاح خاص للـ modals التي تستخدم PrimeReact */
html.dark .p-dialog-mask .p-dialog {
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-header {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-bottom: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-content {
  background: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-footer {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-top: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

/* إصلاح العناصر داخل الـ modals */
html.dark .p-dialog-mask .p-dialog .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

html.dark .p-dialog-mask .p-dialog label,
html.dark .p-dialog-mask .p-dialog .form-label,
html.dark .p-dialog-mask .p-dialog small {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-error {
  color: #ef4444 !important;
}

/* إصلاح شامل للوضع المظلم في الـ modals - تطبيق على جميع العناصر الممكنة */
html.dark .p-dialog-mask .p-dialog .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-calendar-panel,
body.dark .p-dialog-mask .p-dialog .p-dropdown-panel,
body.dark .p-dialog-mask .p-dialog .p-multiselect-panel,
body.dark .p-dialog-mask .p-dialog .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح خاص للـ modals التي تستخدم Tailwind classes */
html.dark .p-dialog-mask .p-dialog .bg-white,
body.dark .p-dialog-mask .p-dialog .bg-white {
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .text-gray-700,
html.dark .p-dialog-mask .p-dialog .text-gray-800,
html.dark .p-dialog-mask .p-dialog .text-gray-900,
body.dark .p-dialog-mask .p-dialog .text-gray-700,
body.dark .p-dialog-mask .p-dialog .text-gray-800,
body.dark .p-dialog-mask .p-dialog .text-gray-900 {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .text-gray-500,
html.dark .p-dialog-mask .p-dialog .text-gray-600,
body.dark .p-dialog-mask .p-dialog .text-gray-500,
body.dark .p-dialog-mask .p-dialog .text-gray-600 {
  color: #cbd5e1 !important;
}

html.dark .p-dialog-mask .p-dialog .border-gray-300,
html.dark .p-dialog-mask .p-dialog .border-gray-200,
body.dark .p-dialog-mask .p-dialog .border-gray-300,
body.dark .p-dialog-mask .p-dialog .border-gray-200 {
  border-color: #475569 !important;
}

html.dark .p-dialog-mask .p-dialog .bg-gray-50,
html.dark .p-dialog-mask .p-dialog .bg-gray-100,
body.dark .p-dialog-mask .p-dialog .bg-gray-50,
body.dark .p-dialog-mask .p-dialog .bg-gray-100 {
  background-color: #334155 !important;
}

html.dark .p-dialog-mask .p-dialog .bg-slate-50,
html.dark .p-dialog-mask .p-dialog .bg-slate-100,
body.dark .p-dialog-mask .p-dialog .bg-slate-50,
body.dark .p-dialog-mask .p-dialog .bg-slate-100 {
  background-color: #334155 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog *,
body.dark .p-dialog-mask .p-dialog * {
  color-scheme: dark !important;
}

/* إصلاح خاص للـ modals التي قد لا تطبق الوضع المظلم تلقائياً */
html.dark .p-dialog-mask .p-dialog,
body.dark .p-dialog-mask .p-dialog {
  color-scheme: dark !important;
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

/* إصلاح شامل لجميع العناصر داخل الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component,
body.dark .p-dialog-mask .p-dialog .p-component {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button,
body.dark .p-dialog-mask .p-dialog .p-component .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary,
body.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح نهائي لجميع الـ modals بغض النظر عن مكان إنشائها */
html.dark .p-dialog,
body.dark .p-dialog {
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.1) !important;
}

html.dark .p-dialog .p-dialog-header,
body.dark .p-dialog .p-dialog-header {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-bottom: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog .p-dialog-content,
body.dark .p-dialog .p-dialog-content {
  background: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog .p-dialog-footer,
body.dark .p-dialog .p-dialog-footer {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-top: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask,
body.dark .p-dialog-mask {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

/* إصلاح شامل لجميع الـ modals بغض النظر عن مكان إنشائها */
html.dark .p-dialog-mask .p-dialog,
body.dark .p-dialog-mask .p-dialog,
html.dark .p-dialog,
body.dark .p-dialog {
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.1) !important;
}

/* إصلاح شامل لجميع عناصر الـ modals */
html.dark .p-dialog-mask .p-dialog .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-inputnumber,
html.dark .p-dialog .p-inputtext,
html.dark .p-dialog .p-inputtextarea,
html.dark .p-dialog .p-dropdown,
html.dark .p-dialog .p-multiselect,
html.dark .p-dialog .p-calendar,
html.dark .p-dialog .p-inputnumber,
body.dark .p-dialog-mask .p-dialog .p-inputtext,
body.dark .p-dialog-mask .p-dialog .p-inputtextarea,
body.dark .p-dialog-mask .p-dialog .p-dropdown,
body.dark .p-dialog-mask .p-dialog .p-multiselect,
body.dark .p-dialog-mask .p-dialog .p-calendar,
body.dark .p-dialog-mask .p-dialog .p-inputnumber,
body.dark .p-dialog .p-inputtext,
body.dark .p-dialog .p-inputtextarea,
body.dark .p-dialog .p-dropdown,
body.dark .p-dialog .p-multiselect,
body.dark .p-dialog .p-calendar,
body.dark .p-dialog .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-inputnumber:focus,
html.dark .p-dialog .p-inputtext:focus,
html.dark .p-dialog .p-inputtextarea:focus,
html.dark .p-dialog .p-dropdown:focus,
html.dark .p-dialog .p-multiselect:focus,
html.dark .p-dialog .p-calendar:focus,
html.dark .p-dialog .p-inputnumber:focus,
body.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
body.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
body.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
body.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
body.dark .p-dialog-mask .p-dialog .p-calendar:focus,
body.dark .p-dialog-mask .p-dialog .p-inputnumber:focus,
body.dark .p-dialog .p-inputtext:focus,
body.dark .p-dialog .p-inputtextarea:focus,
body.dark .p-dialog .p-dropdown:focus,
body.dark .p-dialog .p-multiselect:focus,
body.dark .p-dialog .p-calendar:focus,
body.dark .p-dialog .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-button,
html.dark .p-dialog .p-button,
body.dark .p-dialog-mask .p-dialog .p-button,
body.dark .p-dialog .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary,
html.dark .p-dialog .p-button.p-button-secondary,
body.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary,
body.dark .p-dialog .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

html.dark .p-dialog-mask .p-dialog label,
html.dark .p-dialog-mask .p-dialog .form-label,
html.dark .p-dialog-mask .p-dialog small,
html.dark .p-dialog label,
html.dark .p-dialog .form-label,
html.dark .p-dialog small,
body.dark .p-dialog-mask .p-dialog label,
body.dark .p-dialog-mask .p-dialog .form-label,
body.dark .p-dialog-mask .p-dialog small,
body.dark .p-dialog label,
body.dark .p-dialog .form-label,
body.dark .p-dialog small {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-error,
html.dark .p-dialog .p-error,
body.dark .p-dialog-mask .p-dialog .p-error,
body.dark .p-dialog .p-error {
  color: #ef4444 !important;
}

html.dark .p-dialog-mask .p-dialog .text-slate-700,
html.dark .p-dialog-mask .p-dialog .text-slate-800,
html.dark .p-dialog-mask .p-dialog .text-slate-900,
html.dark .p-dialog .text-slate-700,
html.dark .p-dialog .text-slate-800,
html.dark .p-dialog .text-slate-900,
body.dark .p-dialog-mask .p-dialog .text-slate-700,
body.dark .p-dialog-mask .p-dialog .text-slate-800,
body.dark .p-dialog-mask .p-dialog .text-slate-900,
body.dark .p-dialog .text-slate-700,
body.dark .p-dialog .text-slate-800,
body.dark .p-dialog .text-slate-900 {
  color: #f1f5f9 !important;
}

/* إصلاح نهائي شامل لجميع الـ Tailwind classes في الـ modals */
html.dark .p-dialog-mask .p-dialog .text-gray-700,
html.dark .p-dialog-mask .p-dialog .text-gray-800,
html.dark .p-dialog-mask .p-dialog .text-gray-900,
html.dark .p-dialog .text-gray-700,
html.dark .p-dialog .text-gray-800,
html.dark .p-dialog .text-gray-900,
body.dark .p-dialog-mask .p-dialog .text-gray-700,
body.dark .p-dialog-mask .p-dialog .text-gray-800,
body.dark .p-dialog-mask .p-dialog .text-gray-900,
body.dark .p-dialog .text-gray-700,
body.dark .p-dialog .text-gray-800,
body.dark .p-dialog .text-gray-900 {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .text-gray-500,
html.dark .p-dialog-mask .p-dialog .text-gray-600,
html.dark .p-dialog .text-gray-500,
html.dark .p-dialog .text-gray-600,
body.dark .p-dialog-mask .p-dialog .text-gray-500,
body.dark .p-dialog-mask .p-dialog .text-gray-600,
body.dark .p-dialog .text-gray-500,
body.dark .p-dialog .text-gray-600 {
  color: #cbd5e1 !important;
}

html.dark .p-dialog-mask .p-dialog .border-gray-300,
html.dark .p-dialog-mask .p-dialog .border-gray-200,
html.dark .p-dialog .border-gray-300,
html.dark .p-dialog .border-gray-200,
body.dark .p-dialog-mask .p-dialog .border-gray-300,
body.dark .p-dialog-mask .p-dialog .border-gray-200,
body.dark .p-dialog .border-gray-300,
body.dark .p-dialog .border-gray-200 {
  border-color: #475569 !important;
}

html.dark .p-dialog-mask .p-dialog .bg-gray-50,
html.dark .p-dialog-mask .p-dialog .bg-gray-100,
html.dark .p-dialog .bg-gray-50,
html.dark .p-dialog .bg-gray-100,
body.dark .p-dialog-mask .p-dialog .bg-gray-50,
body.dark .p-dialog-mask .p-dialog .bg-gray-100,
body.dark .p-dialog .bg-gray-50,
body.dark .p-dialog .bg-gray-100 {
  background-color: #334155 !important;
}

html.dark .p-dialog-mask .p-dialog .bg-white,
html.dark .p-dialog .bg-white,
body.dark .p-dialog-mask .p-dialog .bg-white,
body.dark .p-dialog .bg-white {
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .text-slate-500,
html.dark .p-dialog-mask .p-dialog .text-slate-600,
html.dark .p-dialog .text-slate-500,
html.dark .p-dialog .text-slate-600,
body.dark .p-dialog-mask .p-dialog .text-slate-500,
body.dark .p-dialog-mask .p-dialog .text-slate-600,
body.dark .p-dialog .text-slate-500,
body.dark .p-dialog .text-slate-600 {
  color: #cbd5e1 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component,
html.dark .p-dialog .p-component,
body.dark .p-dialog-mask .p-dialog .p-component,
body.dark .p-dialog .p-component {
  color: #f1f5f9 !important;
}

/* إصلاح نهائي شامل لجميع الـ modals بغض النظر عن مكان إنشائها */
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber,
html.dark .p-dialog .p-component .p-inputtext,
html.dark .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog .p-component .p-dropdown,
html.dark .p-dialog .p-component .p-multiselect,
html.dark .p-dialog .p-component .p-calendar,
html.dark .p-dialog .p-component .p-inputnumber,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber,
body.dark .p-dialog .p-component .p-inputtext,
body.dark .p-dialog .p-component .p-inputtextarea,
body.dark .p-dialog .p-component .p-dropdown,
body.dark .p-dialog .p-component .p-multiselect,
body.dark .p-dialog .p-component .p-calendar,
body.dark .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus,
html.dark .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog .p-component .p-inputnumber:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus,
body.dark .p-dialog .p-component .p-inputtext:focus,
body.dark .p-dialog .p-component .p-inputtextarea:focus,
body.dark .p-dialog .p-component .p-dropdown:focus,
body.dark .p-dialog .p-component .p-multiselect:focus,
body.dark .p-dialog .p-component .p-calendar:focus,
body.dark .p-dialog .p-component .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button,
html.dark .p-dialog .p-component .p-button,
body.dark .p-dialog-mask .p-dialog .p-component .p-button,
body.dark .p-dialog .p-component .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary,
html.dark .p-dialog .p-component .p-button.p-button-secondary,
body.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary,
body.dark .p-dialog .p-component .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

html.dark .p-dialog-mask .p-dialog .border-slate-300,
html.dark .p-dialog-mask .p-dialog .border-slate-200,
html.dark .p-dialog .border-slate-300,
html.dark .p-dialog .border-slate-200,
body.dark .p-dialog-mask .p-dialog .border-slate-300,
body.dark .p-dialog-mask .p-dialog .border-slate-200,
body.dark .p-dialog .border-slate-300,
body.dark .p-dialog .border-slate-200 {
  border-color: #475569 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel,
html.dark .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog .p-component .p-calendar-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel,
body.dark .p-dialog .p-component .p-dropdown-panel,
body.dark .p-dialog .p-component .p-multiselect-panel,
body.dark .p-dialog .p-component .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .dark\\:from-slate-800,
html.dark .p-dialog-mask .p-dialog .dark\\:to-gray-800,
html.dark .p-dialog-mask .p-dialog .dark\\:border-slate-600,
html.dark .p-dialog .dark\\:from-slate-800,
html.dark .p-dialog .dark\\:to-gray-800,
html.dark .p-dialog .dark\\:border-slate-600,
body.dark .p-dialog-mask .p-dialog .dark\\:from-slate-800,
body.dark .p-dialog-mask .p-dialog .dark\\:to-gray-800,
body.dark .p-dialog-mask .p-dialog .dark\\:border-slate-600,
body.dark .p-dialog .dark\\:from-slate-800,
body.dark .p-dialog .dark\\:to-gray-800,
body.dark .p-dialog .dark\\:border-slate-600 {
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border-color: #475569 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .dark\\:text-slate-200,
html.dark .p-dialog-mask .p-dialog .dark\\:text-slate-300,
html.dark .p-dialog .dark\\:text-slate-200,
html.dark .p-dialog .dark\\:text-slate-300,
body.dark .p-dialog-mask .p-dialog .dark\\:text-slate-200,
body.dark .p-dialog-mask .p-dialog .dark\\:text-slate-300,
body.dark .p-dialog .dark\\:text-slate-200,
body.dark .p-dialog .dark\\:text-slate-300 {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .dark\\:bg-slate-800,
html.dark .p-dialog .dark\\:bg-slate-800,
body.dark .p-dialog-mask .p-dialog .dark\\:bg-slate-800,
body.dark .p-dialog .dark\\:bg-slate-800 {
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .dark\\:shadow-lg,
html.dark .p-dialog .dark\\:shadow-lg,
body.dark .p-dialog-mask .p-dialog .dark\\:shadow-lg,
body.dark .p-dialog .dark\\:shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog,
body.dark .p-dialog-mask .p-dialog {
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.1) !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog *,
body.dark .p-dialog-mask .p-dialog * {
  color-scheme: dark !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component,
body.dark .p-dialog-mask .p-dialog .p-component {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .dark\\:text-slate-200,
html.dark .p-dialog-mask .p-dialog .dark\\:text-slate-300,
html.dark .p-dialog .dark\\:text-slate-200,
html.dark .p-dialog .dark\\:text-slate-300,
body.dark .p-dialog-mask .p-dialog .dark\\:text-slate-200,
body.dark .p-dialog-mask .p-dialog .dark\\:text-slate-300,
body.dark .p-dialog .dark\\:text-slate-200,
body.dark .p-dialog .dark\\:text-slate-300 {
  color: #f1f5f9 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber,
html.dark .p-dialog .p-component .p-inputtext,
html.dark .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog .p-component .p-dropdown,
html.dark .p-dialog .p-component .p-multiselect,
html.dark .p-dialog .p-component .p-calendar,
html.dark .p-dialog .p-component .p-inputnumber,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber,
body.dark .p-dialog .p-component .p-inputtext,
body.dark .p-dialog .p-component .p-inputtextarea,
body.dark .p-dialog .p-component .p-dropdown,
body.dark .p-dialog .p-component .p-multiselect,
body.dark .p-dialog .p-component .p-calendar,
body.dark .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus,
html.dark .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog .p-component .p-inputnumber:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus,
body.dark .p-dialog .p-component .p-inputtext:focus,
body.dark .p-dialog .p-component .p-inputtextarea:focus,
body.dark .p-dialog .p-component .p-dropdown:focus,
body.dark .p-dialog .p-component .p-multiselect:focus,
body.dark .p-dialog .p-component .p-calendar:focus,
body.dark .p-dialog .p-component .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button,
html.dark .p-dialog .p-component .p-button,
body.dark .p-dialog-mask .p-dialog .p-component .p-button,
body.dark .p-dialog .p-component .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary,
html.dark .p-dialog .p-component .p-button.p-button-secondary,
body.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary,
body.dark .p-dialog .p-component .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

html.dark .p-dialog-mask .p-dialog .dark\\:bg-slate-800,
html.dark .p-dialog-mask .p-dialog .dark\\:bg-slate-700,
html.dark .p-dialog .dark\\:bg-slate-800,
html.dark .p-dialog .dark\\:bg-slate-700,
body.dark .p-dialog-mask .p-dialog .dark\\:bg-slate-800,
body.dark .p-dialog-mask .p-dialog .dark\\:bg-slate-700,
body.dark .p-dialog .dark\\:bg-slate-800,
body.dark .p-dialog .dark\\:bg-slate-700 {
  background-color: #1e293b !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel,
html.dark .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog .p-component .p-calendar-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel,
body.dark .p-dialog .p-component .p-dropdown-panel,
body.dark .p-dialog .p-component .p-multiselect-panel,
body.dark .p-dialog .p-component .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .dark\\:shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
}

/* إصلاح خاص للـ modals التي تستخدم CSS variables */
html.dark .p-dialog-mask .p-dialog {
  --surface: #1e293b !important;
  --bg-secondary: #334155 !important;
  --bg-tertiary: #475569 !important;
  --text-primary: #f1f5f9 !important;
  --text-secondary: #cbd5e1 !important;
  --text-tertiary: #94a3b8 !important;
  --border: #475569 !important;
  --accent: #3b82f6 !important;
  --accent-hover: #2563eb !important;
  --surface-hover: #334155 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask,
body.dark .p-dialog-mask {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

/* إصلاح شامل لجميع الـ modals بغض النظر عن مكان إنشائها */
html.dark .p-dialog-mask .p-dialog,
body.dark .p-dialog-mask .p-dialog,
html.dark .p-dialog,
body.dark .p-dialog {
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.1) !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-header,
body.dark .p-dialog-mask .p-dialog .p-dialog-header,
html.dark .p-dialog .p-dialog-header,
body.dark .p-dialog .p-dialog-header {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-bottom: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-content,
body.dark .p-dialog-mask .p-dialog .p-dialog-content,
html.dark .p-dialog .p-dialog-content,
body.dark .p-dialog .p-dialog-content {
  background: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dialog-footer,
body.dark .p-dialog-mask .p-dialog .p-dialog-footer,
html.dark .p-dialog .p-dialog-footer,
body.dark .p-dialog .p-dialog-footer {
  background: linear-gradient(135deg, #334155, #475569) !important;
  border-top: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

/* إصلاح شامل لجميع عناصر الـ modals */
html.dark .p-dialog-mask .p-dialog .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-inputnumber,
html.dark .p-dialog .p-inputtext,
html.dark .p-dialog .p-inputtextarea,
html.dark .p-dialog .p-dropdown,
html.dark .p-dialog .p-multiselect,
html.dark .p-dialog .p-calendar,
html.dark .p-dialog .p-inputnumber,
body.dark .p-dialog-mask .p-dialog .p-inputtext,
body.dark .p-dialog-mask .p-dialog .p-inputtextarea,
body.dark .p-dialog-mask .p-dialog .p-dropdown,
body.dark .p-dialog-mask .p-dialog .p-multiselect,
body.dark .p-dialog-mask .p-dialog .p-calendar,
body.dark .p-dialog-mask .p-dialog .p-inputnumber,
body.dark .p-dialog .p-inputtext,
body.dark .p-dialog .p-inputtextarea,
body.dark .p-dialog .p-dropdown,
body.dark .p-dialog .p-multiselect,
body.dark .p-dialog .p-calendar,
body.dark .p-dialog .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-inputnumber:focus,
html.dark .p-dialog .p-inputtext:focus,
html.dark .p-dialog .p-inputtextarea:focus,
html.dark .p-dialog .p-dropdown:focus,
html.dark .p-dialog .p-multiselect:focus,
html.dark .p-dialog .p-calendar:focus,
html.dark .p-dialog .p-inputnumber:focus,
body.dark .p-dialog-mask .p-dialog .p-inputtext:focus,
body.dark .p-dialog-mask .p-dialog .p-inputtextarea:focus,
body.dark .p-dialog-mask .p-dialog .p-dropdown:focus,
body.dark .p-dialog-mask .p-dialog .p-multiselect:focus,
body.dark .p-dialog-mask .p-dialog .p-calendar:focus,
body.dark .p-dialog-mask .p-dialog .p-inputnumber:focus,
body.dark .p-dialog .p-inputtext:focus,
body.dark .p-dialog .p-inputtextarea:focus,
body.dark .p-dialog .p-dropdown:focus,
body.dark .p-dialog .p-multiselect:focus,
body.dark .p-dialog .p-calendar:focus,
body.dark .p-dialog .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-button,
html.dark .p-dialog .p-button,
body.dark .p-dialog-mask .p-dialog .p-button,
body.dark .p-dialog .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary,
html.dark .p-dialog .p-button.p-button-secondary,
body.dark .p-dialog-mask .p-dialog .p-button.p-button-secondary,
body.dark .p-dialog .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

html.dark .p-dialog-mask .p-dialog label,
html.dark .p-dialog-mask .p-dialog .form-label,
html.dark .p-dialog-mask .p-dialog small,
html.dark .p-dialog label,
html.dark .p-dialog .form-label,
html.dark .p-dialog small,
body.dark .p-dialog-mask .p-dialog label,
body.dark .p-dialog-mask .p-dialog .form-label,
body.dark .p-dialog-mask .p-dialog small,
body.dark .p-dialog label,
body.dark .p-dialog .form-label,
body.dark .p-dialog small {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-error,
html.dark .p-dialog .p-error,
body.dark .p-dialog-mask .p-dialog .p-error,
body.dark .p-dialog .p-error {
  color: #ef4444 !important;
}

/* إصلاح شامل للوضع المظلم في الـ modals - تطبيق على جميع العناصر الممكنة */
html.dark .p-dialog-mask .p-dialog .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-calendar-panel,
html.dark .p-dialog .p-dropdown-panel,
html.dark .p-dialog .p-multiselect-panel,
html.dark .p-dialog .p-calendar-panel,
body.dark .p-dialog-mask .p-dialog .p-dropdown-panel,
body.dark .p-dialog-mask .p-dialog .p-multiselect-panel,
body.dark .p-dialog-mask .p-dialog .p-calendar-panel,
body.dark .p-dialog .p-dropdown-panel,
body.dark .p-dialog .p-multiselect-panel,
body.dark .p-dialog .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item,
html.dark .p-dialog .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item:hover,
html.dark .p-dialog .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item.p-highlight,
html.dark .p-dialog .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح خاص للـ modals التي تستخدم Tailwind classes */
html.dark .p-dialog-mask .p-dialog .bg-white,
html.dark .p-dialog .bg-white,
body.dark .p-dialog-mask .p-dialog .bg-white,
body.dark .p-dialog .bg-white {
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .text-gray-700,
html.dark .p-dialog-mask .p-dialog .text-gray-800,
html.dark .p-dialog-mask .p-dialog .text-gray-900,
html.dark .p-dialog .text-gray-700,
html.dark .p-dialog .text-gray-800,
html.dark .p-dialog .text-gray-900,
body.dark .p-dialog-mask .p-dialog .text-gray-700,
body.dark .p-dialog-mask .p-dialog .text-gray-800,
body.dark .p-dialog-mask .p-dialog .text-gray-900,
body.dark .p-dialog .text-gray-700,
body.dark .p-dialog .text-gray-800,
body.dark .p-dialog .text-gray-900 {
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .text-gray-500,
html.dark .p-dialog-mask .p-dialog .text-gray-600,
html.dark .p-dialog .text-gray-500,
html.dark .p-dialog .text-gray-600,
body.dark .p-dialog-mask .p-dialog .text-gray-500,
body.dark .p-dialog-mask .p-dialog .text-gray-600,
body.dark .p-dialog .text-gray-500,
body.dark .p-dialog .text-gray-600 {
  color: #cbd5e1 !important;
}

html.dark .p-dialog-mask .p-dialog .border-gray-300,
html.dark .p-dialog-mask .p-dialog .border-gray-200,
html.dark .p-dialog .border-gray-300,
html.dark .p-dialog .border-gray-200,
body.dark .p-dialog-mask .p-dialog .border-gray-300,
body.dark .p-dialog-mask .p-dialog .border-gray-200,
body.dark .p-dialog .border-gray-300,
body.dark .p-dialog .border-gray-200 {
  border-color: #475569 !important;
}

html.dark .p-dialog-mask .p-dialog .bg-gray-50,
html.dark .p-dialog-mask .p-dialog .bg-gray-100,
html.dark .p-dialog .bg-gray-50,
html.dark .p-dialog .bg-gray-100,
body.dark .p-dialog-mask .p-dialog .bg-gray-50,
body.dark .p-dialog-mask .p-dialog .bg-gray-100,
body.dark .p-dialog .bg-gray-50,
body.dark .p-dialog .bg-gray-100 {
  background-color: #334155 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog *,
body.dark .p-dialog-mask .p-dialog *,
html.dark .p-dialog *,
body.dark .p-dialog * {
  color-scheme: dark !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog,
body.dark .p-dialog-mask .p-dialog,
html.dark .p-dialog,
body.dark .p-dialog {
  color-scheme: dark !important;
  background: linear-gradient(145deg, #1e293b, #334155) !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog * {
  color-scheme: dark !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component,
body.dark .p-dialog-mask .p-dialog .p-component,
html.dark .p-dialog .p-component,
body.dark .p-dialog .p-component {
  color: #f1f5f9 !important;
}

/* إصلاح شامل لجميع الـ modals بغض النظر عن مكان إنشائها */
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber,
html.dark .p-dialog .p-component .p-inputtext,
html.dark .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog .p-component .p-dropdown,
html.dark .p-dialog .p-component .p-multiselect,
html.dark .p-dialog .p-component .p-calendar,
html.dark .p-dialog .p-component .p-inputnumber,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber,
body.dark .p-dialog .p-component .p-inputtext,
body.dark .p-dialog .p-component .p-inputtextarea,
body.dark .p-dialog .p-component .p-dropdown,
body.dark .p-dialog .p-component .p-multiselect,
body.dark .p-dialog .p-component .p-calendar,
body.dark .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus,
html.dark .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog .p-component .p-inputnumber:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
body.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus,
body.dark .p-dialog .p-component .p-inputtext:focus,
body.dark .p-dialog .p-component .p-inputtextarea:focus,
body.dark .p-dialog .p-component .p-dropdown:focus,
body.dark .p-dialog .p-component .p-multiselect:focus,
body.dark .p-dialog .p-component .p-calendar:focus,
body.dark .p-dialog .p-component .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button,
html.dark .p-dialog .p-component .p-button,
body.dark .p-dialog-mask .p-dialog .p-component .p-button,
body.dark .p-dialog .p-component .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary,
html.dark .p-dialog .p-component .p-button.p-button-secondary,
body.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary,
body.dark .p-dialog .p-component .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel,
html.dark .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog .p-component .p-calendar-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
body.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel,
body.dark .p-dialog .p-component .p-dropdown-panel,
body.dark .p-dialog .p-component .p-multiselect-panel,
body.dark .p-dialog .p-component .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
html.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight,
body.dark .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
body.dark .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح خاص للـ modals التي تستخدم PrimeReact */
html.dark .p-dialog-mask .p-dialog .p-component {
  color: #f1f5f9 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle {
  color: #cbd5e1 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle:hover {
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle {
  color: #cbd5e1 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle:hover {
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح خاص للـ modals التي تستخدم PrimeReact */
html.dark .p-dialog-mask .p-dialog .p-component .p-password {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle {
  color: #cbd5e1 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle:hover {
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

/* إصلاح نهائي شامل لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber {
  background-color: #334155 !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar:focus,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputnumber:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button {
  background: linear-gradient(135deg, #3b82f6, #2563eb) !important;
  border: none !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-button.p-button-secondary {
  background: linear-gradient(135deg, #334155, #475569) !important;
  color: #f1f5f9 !important;
  border: 1px solid #475569 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-panel,
html.dark .p-dialog-mask .p-dialog .p-component .p-calendar-panel {
  background-color: #1e293b !important;
  border: 1px solid #475569 !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item:hover,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item:hover {
  background-color: #334155 !important;
  color: #3b82f6 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-dropdown-items .p-dropdown-item.p-highlight,
html.dark .p-dialog-mask .p-dialog .p-component .p-multiselect-items .p-multiselect-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input {
  background-color: transparent !important;
  color: #f1f5f9 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle {
  color: #cbd5e1 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-toggle:hover {
  color: #3b82f6 !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea::placeholder {
  color: #94a3b8 !important;
}

/* إصلاح خاص للـ modals التي تستخدم PrimeReact */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

/* إصلاح خاص للـ modals التي تستخدم PrimeReact */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #1e293b !important;
}

/* إصلاح نهائي لضمان تطبيق الوضع المظلم على جميع الـ modals */
html.dark .p-dialog-mask .p-dialog .p-component .p-password .p-password-input:focus::placeholder {
  color: #94a3b8 !important;
}

html.dark .p-dialog-mask .p-dialog .p-component .p-inputtext:focus::placeholder,
html.dark .p-dialog-mask .p-dialog .p-component .p-inputtextarea:focus::placeholder {
  color: #94a3b8 !important;
}

.dark .p-dialog .p-dialog-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
  position: relative !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
  position: relative !important;
}

.dark .p-dialog .p-dialog-header::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, var(--accent), transparent) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-header::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, var(--accent), transparent) !important;
}

.dark .p-dialog .p-dialog-header .p-dialog-title {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 1.25rem !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-header .p-dialog-title {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  font-size: 1.25rem !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.dark .p-dialog .p-dialog-header .p-dialog-header-icon {
  color: var(--text-secondary) !important;
  transition: all 0.3s ease !important;
  border-radius: 8px !important;
  padding: 0.5rem !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-header .p-dialog-header-icon {
  color: var(--text-secondary) !important;
  transition: all 0.3s ease !important;
  border-radius: 8px !important;
  padding: 0.5rem !important;
}

.dark .p-dialog .p-dialog-header .p-dialog-header-icon:hover {
  color: var(--accent) !important;
  background-color: var(--surface-hover) !important;
  transform: scale(1.1) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-header .p-dialog-header-icon:hover {
  color: var(--accent) !important;
  background-color: var(--surface-hover) !important;
  transform: scale(1.1) !important;
}

.dark .p-dialog .p-dialog-content {
  background: var(--surface) !important;
  color: var(--text-primary) !important;
  padding: 2rem !important;
  border-radius: 0 0 16px 16px !important;
  position: relative !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-content {
  background: var(--surface) !important;
  color: var(--text-primary) !important;
  padding: 2rem !important;
  border-radius: 0 0 16px 16px !important;
  position: relative !important;
}

.dark .p-dialog .p-dialog-content::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, var(--border), transparent) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-content::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, var(--border), transparent) !important;
}

.dark .p-dialog .p-dialog-footer {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-top: 1px solid var(--border) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
  position: relative !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-footer {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-top: 1px solid var(--border) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
  position: relative !important;
}

.dark .p-dialog .p-dialog-footer::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, var(--accent), transparent) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-dialog-footer::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, var(--accent), transparent) !important;
}

/* تحسين العناصر داخل الـ Dialogs */
.dark .p-dialog .p-inputtext {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.3s ease !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-inputtext {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.3s ease !important;
}

.dark .p-dialog .p-inputtext:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: var(--surface) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-inputtext:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: var(--surface) !important;
}

.dark .p-dialog .p-inputtext::placeholder {
  color: var(--text-tertiary) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-inputtext::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-dialog .p-button {
  background: linear-gradient(135deg, var(--accent), var(--accent-hover)) !important;
  border: none !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog .p-button {
  background: linear-gradient(135deg, var(--accent), var(--accent-hover)) !important;
  border: none !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.dark .p-dialog .p-button:hover {
  background: linear-gradient(135deg, var(--accent-hover), var(--accent)) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4) !important;
}

.dark .p-dialog .p-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.dark .p-dialog .p-button.p-button-secondary {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.dark .p-dialog .p-button.p-button-secondary:hover {
  background: linear-gradient(135deg, var(--surface-hover), var(--surface)) !important;
  border-color: var(--accent) !important;
  color: var(--accent) !important;
}

.dark .p-dialog .p-dropdown {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
}

.dark .p-dialog .p-dropdown:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.dark .p-dialog .p-dropdown-panel {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .p-dialog .p-dropdown-item {
  color: var(--text-primary) !important;
  transition: all 0.2s ease !important;
}

.dark .p-dialog .p-dropdown-item:hover {
  background-color: var(--surface-hover) !important;
  color: var(--accent) !important;
}

.dark .p-dialog .p-dropdown-item.p-highlight {
  background-color: var(--accent) !important;
  color: white !important;
}

.dark .p-dialog .p-checkbox .p-checkbox-box {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
  border-radius: 4px !important;
}

.dark .p-dialog .p-checkbox .p-checkbox-box.p-highlight {
  background-color: var(--accent) !important;
  border-color: var(--accent) !important;
}

.dark .p-dialog .p-checkbox .p-checkbox-label {
  color: var(--text-primary) !important;
}

.dark .p-dialog .p-radiobutton .p-radiobutton-box {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
}

.dark .p-dialog .p-radiobutton .p-radiobutton-box.p-highlight {
  background-color: var(--accent) !important;
  border-color: var(--accent) !important;
}

.dark .p-dialog .p-radiobutton .p-radiobutton-label {
  color: var(--text-primary) !important;
}

.dark .p-dialog .p-calendar {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
  border-radius: 8px !important;
}

.dark .p-dialog .p-calendar:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.dark .p-dialog .p-calendar-panel {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .p-dialog .p-calendar-panel .p-datepicker-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-dialog .p-calendar-panel .p-datepicker-calendar th {
  color: var(--text-secondary) !important;
}

.dark .p-dialog .p-calendar-panel .p-datepicker-calendar td > span {
  color: var(--text-primary) !important;
  transition: all 0.2s ease !important;
}

.dark .p-dialog .p-calendar-panel .p-datepicker-calendar td > span:hover {
  background-color: var(--surface-hover) !important;
  color: var(--accent) !important;
}

.dark .p-dialog .p-calendar-panel .p-datepicker-calendar td > span.p-highlight {
  background-color: var(--accent) !important;
  color: white !important;
}

.dark .p-dialog .p-tabview .p-tabview-nav {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
}

.dark .p-dialog .p-tabview .p-tabview-nav li .p-tabview-nav-link {
  color: var(--text-secondary) !important;
  background-color: transparent !important;
  border: none !important;
  transition: all 0.3s ease !important;
}

.dark .p-dialog .p-tabview .p-tabview-nav li .p-tabview-nav-link:hover {
  color: var(--accent) !important;
  background-color: var(--surface-hover) !important;
}

.dark .p-dialog .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  color: var(--accent) !important;
  background-color: var(--surface) !important;
  border-bottom: 2px solid var(--accent) !important;
}

.dark .p-dialog .p-tabview .p-tabview-panels {
  background-color: var(--surface) !important;
  color: var(--text-primary) !important;
}

/* تحسين الـ ConfirmDialog للوضع المظلم */
.dark .p-confirm-dialog {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 16px !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .p-confirm-dialog .p-dialog-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
}

.dark .p-confirm-dialog .p-dialog-content {
  background: var(--surface) !important;
  color: var(--text-primary) !important;
  padding: 2rem !important;
}

.dark .p-confirm-dialog .p-dialog-footer {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-top: 1px solid var(--border) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
}

/* تحسين الـ Toast للوضع المظلم */
.dark .p-toast {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .p-toast .p-toast-message {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  color: var(--text-primary) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.dark .p-toast .p-toast-message-content {
  color: var(--text-primary) !important;
}

.dark .p-toast .p-toast-message.p-toast-message-success {
  background: linear-gradient(145deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1)) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.dark .p-toast .p-toast-message.p-toast-message-error {
  background: linear-gradient(145deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1)) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.dark .p-toast .p-toast-message.p-toast-message-warn {
  background: linear-gradient(145deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1)) !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.dark .p-toast .p-toast-message.p-toast-message-info {
  background: linear-gradient(145deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1)) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

/* تحسين الـ Overlay والـ Backdrop للوضع المظلم */
.dark .p-dialog-mask {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-dialog-mask {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

.dark .p-confirm-dialog-mask {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

/* إصلاح مشكلة الوضع المظلم للـ modals - تطبيق مباشر على document */
html.dark .p-confirm-dialog-mask {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

.dark .p-toast-mask {
  background-color: transparent !important;
}

/* تحسين الـ Sidebar للوضع المظلم */
.dark .p-sidebar {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

.dark .p-sidebar .p-sidebar-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-sidebar .p-sidebar-content {
  background: var(--surface) !important;
  color: var(--text-primary) !important;
}

/* تحسين الـ Tooltip للوضع المظلم */
.dark .p-tooltip {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 8px !important;
  color: var(--text-primary) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .p-tooltip .p-tooltip-arrow {
  border-color: var(--border) !important;
}

/* تحسين الـ ProgressBar للوضع المظلم */
.dark .p-progressbar {
  background-color: var(--bg-secondary) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.dark .p-progressbar .p-progressbar-value {
  background: linear-gradient(90deg, var(--accent), var(--accent-hover)) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

/* تحسين الـ Spinner للوضع المظلم */
.dark .p-progress-spinner-circle {
  stroke: var(--accent) !important;
}

/* تحسين الـ FileUpload للوضع المظلم */
.dark .p-fileupload {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
}

.dark .p-fileupload .p-fileupload-buttonbar {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
}

.dark .p-fileupload .p-fileupload-content {
  background-color: var(--surface) !important;
  color: var(--text-primary) !important;
}

/* تحسين الـ DataTable داخل الـ Dialogs */
.dark .p-dialog .p-datatable {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 8px !important;
}

.dark .p-dialog .p-datatable .p-datatable-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-dialog .p-datatable .p-datatable-thead > tr > th {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-dialog .p-datatable .p-datatable-tbody > tr {
  background-color: var(--surface) !important;
  color: var(--text-primary) !important;
}

.dark .p-dialog .p-datatable .p-datatable-tbody > tr:hover {
  background-color: var(--surface-hover) !important;
}

.dark .p-dialog .p-datatable .p-datatable-tbody > tr > td {
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقل Select Team Members للوضع المظلم */
/* تحسينات احترافية للـ MultiSelect */
.p-multiselect {
  background: linear-gradient(145deg, #ffffff, #f8fafc) !important;
  border: 2px solid #e2e8f0 !important;
  color: #1e293b !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-height: 48px !important;
  position: relative !important;
  overflow: hidden !important;
}

.p-multiselect::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.p-multiselect:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-1px) !important;
}

.p-multiselect:hover::before {
  opacity: 1 !important;
}

.dark .p-multiselect {
  background: linear-gradient(145deg, var(--bg-secondary), var(--surface)) !important;
  border: 2px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-height: 48px !important;
  position: relative !important;
  overflow: hidden !important;
}

.dark .p-multiselect::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.dark .p-multiselect:hover {
  border-color: var(--accent) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-1px) !important;
}

.dark .p-multiselect:hover::before {
  opacity: 1 !important;
}

.p-multiselect:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15), 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-2px) !important;
}

.p-multiselect:focus::before {
  opacity: 1 !important;
}

.dark .p-multiselect:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2), 0 10px 15px -3px rgba(0, 0, 0, 0.4) !important;
  transform: translateY(-2px) !important;
}

.dark .p-multiselect:focus::before {
  opacity: 1 !important;
}

/* تحسين أنماط الـ Label والـ Placeholder */
.p-multiselect .p-multiselect-label {
  color: #1e293b !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
}

.p-multiselect .p-multiselect-label.p-placeholder {
  color: #64748b !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

.dark .p-multiselect .p-multiselect-label {
  color: var(--text-primary) !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
}

.dark .p-multiselect .p-multiselect-label.p-placeholder {
  color: var(--text-tertiary) !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

/* تحسين أنماط الـ Trigger (السهم) */
.p-multiselect .p-multiselect-trigger {
  color: #64748b !important;
  transition: all 0.2s ease !important;
  padding: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  height: 48px !important;
}

.p-multiselect .p-multiselect-trigger:hover {
  color: #3b82f6 !important;
  background-color: rgba(59, 130, 246, 0.05) !important;
  border-radius: 8px !important;
}

.p-multiselect .p-multiselect-trigger .p-multiselect-trigger-icon {
  transition: transform 0.2s ease !important;
  font-size: 0.875rem !important;
}

.p-multiselect.p-multiselect-open .p-multiselect-trigger .p-multiselect-trigger-icon {
  transform: rotate(180deg) !important;
}

.dark .p-multiselect .p-multiselect-trigger {
  color: var(--text-secondary) !important;
  transition: all 0.2s ease !important;
  padding: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  height: 48px !important;
}

.dark .p-multiselect .p-multiselect-trigger:hover {
  color: var(--accent) !important;
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 8px !important;
}

.dark .p-multiselect .p-multiselect-trigger .p-multiselect-trigger-icon {
  transition: transform 0.2s ease !important;
  font-size: 0.875rem !important;
}

.dark .p-multiselect.p-multiselect-open .p-multiselect-trigger .p-multiselect-trigger-icon {
  transform: rotate(180deg) !important;
}

/* تحسين أنماط الـ Panel (القائمة المنسدلة) */
.p-multiselect-panel {
  background: linear-gradient(145deg, #ffffff, #f8fafc) !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  backdrop-filter: blur(20px) !important;
  overflow: hidden !important;
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin-top: 12px !important;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dark .p-multiselect-panel {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(20px) !important;
  overflow: hidden !important;
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin-top: 12px !important;
}

/* تحسين أنماط العناصر في القائمة */
.p-multiselect-panel .p-multiselect-items {
  background: transparent !important;
  padding: 0.5rem 0 !important;
}

.p-multiselect-panel .p-multiselect-item {
  color: #1e293b !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0.75rem 1rem !important;
  margin: 0.125rem 0.5rem !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.p-multiselect-panel .p-multiselect-item::before {
  content: '' !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  width: 3px !important;
  background: linear-gradient(180deg, #3b82f6, #8b5cf6) !important;
  transform: scaleY(0) !important;
  transition: transform 0.2s ease !important;
}

.p-multiselect-panel .p-multiselect-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1)) !important;
  color: #3b82f6 !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

.p-multiselect-panel .p-multiselect-item:hover::before {
  transform: scaleY(1) !important;
}

.p-multiselect-panel .p-multiselect-item.p-highlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  color: white !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.p-multiselect-panel .p-multiselect-item.p-highlight::before {
  transform: scaleY(1) !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

.dark .p-multiselect-panel .p-multiselect-items {
  background: transparent !important;
  padding: 0.5rem 0 !important;
}

.dark .p-multiselect-panel .p-multiselect-item {
  color: var(--text-primary) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0.75rem 1rem !important;
  margin: 0.125rem 0.5rem !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.dark .p-multiselect-panel .p-multiselect-item::before {
  content: '' !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  width: 3px !important;
  background: linear-gradient(180deg, var(--accent), #8b5cf6) !important;
  transform: scaleY(0) !important;
  transition: transform 0.2s ease !important;
}

.dark .p-multiselect-panel .p-multiselect-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15)) !important;
  color: var(--accent) !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
}

.dark .p-multiselect-panel .p-multiselect-item:hover::before {
  transform: scaleY(1) !important;
}

.dark .p-multiselect-panel .p-multiselect-item.p-highlight {
  background: linear-gradient(135deg, var(--accent), #8b5cf6) !important;
  color: white !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

.dark .p-multiselect-panel .p-multiselect-item.p-highlight::before {
  transform: scaleY(1) !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

.dark .p-multiselect-panel .p-multiselect-item .p-checkbox {
  margin-right: 0.5rem !important;
}

.dark .p-multiselect-panel .p-multiselect-item .p-checkbox .p-checkbox-box {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border) !important;
  border-radius: 4px !important;
}

.dark .p-multiselect-panel .p-multiselect-item .p-checkbox .p-checkbox-box.p-highlight {
  background-color: var(--accent) !important;
  border-color: var(--accent) !important;
}

.dark .p-multiselect-panel .p-multiselect-item .p-checkbox .p-checkbox-box .p-checkbox-icon {
  color: white !important;
}

/* تحسين أنماط الـ Chips (العناصر المحددة) */
.p-multiselect .p-multiselect-token {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  color: white !important;
  border-radius: 20px !important;
  padding: 0.375rem 0.75rem !important;
  margin: 0.125rem !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2) !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  overflow: hidden !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.p-multiselect .p-multiselect-token::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
  transition: left 0.5s ease !important;
}

.p-multiselect .p-multiselect-token:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3) !important;
}

.p-multiselect .p-multiselect-token:hover::before {
  left: 100% !important;
}

.p-multiselect .p-multiselect-token .p-multiselect-token-label {
  color: white !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.p-multiselect .p-multiselect-token .p-multiselect-token-icon {
  color: white !important;
  margin-left: 0.5rem !important;
  transition: all 0.2s ease !important;
  border-radius: 50% !important;
  padding: 0.125rem !important;
  width: 16px !important;
  height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.p-multiselect .p-multiselect-token .p-multiselect-token-icon:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1) !important;
}

.dark .p-multiselect .p-multiselect-token {
  background: linear-gradient(135deg, var(--accent), #8b5cf6) !important;
  color: white !important;
  border-radius: 20px !important;
  padding: 0.375rem 0.75rem !important;
  margin: 0.125rem !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3) !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  overflow: hidden !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dark .p-multiselect .p-multiselect-token::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
  transition: left 0.5s ease !important;
}

.dark .p-multiselect .p-multiselect-token:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4) !important;
}

.dark .p-multiselect .p-multiselect-token:hover::before {
  left: 100% !important;
}

.dark .p-multiselect .p-multiselect-token .p-multiselect-token-label {
  color: white !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.dark .p-multiselect .p-multiselect-token .p-multiselect-token-icon {
  color: white !important;
  margin-left: 0.5rem !important;
  transition: all 0.2s ease !important;
  border-radius: 50% !important;
  padding: 0.125rem !important;
  width: 16px !important;
  height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.dark .p-multiselect .p-multiselect-token .p-multiselect-token-icon:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1) !important;
}

/* تحسين أنماط الـ Header في الـ Multiselect */
.p-multiselect-header {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
  border-bottom: 2px solid #e2e8f0 !important;
  color: #1e293b !important;
  padding: 1rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.p-multiselect-header::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4) !important;
}

.p-multiselect-header .p-multiselect-header-checkbox {
  background: linear-gradient(145deg, #ffffff, #f1f5f9) !important;
  border: 2px solid #cbd5e1 !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.p-multiselect-header .p-multiselect-header-checkbox:hover {
  border-color: #3b82f6 !important;
  transform: scale(1.05) !important;
}

.p-multiselect-header .p-multiselect-header-checkbox.p-highlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3) !important;
}

.p-multiselect-header .p-multiselect-header-checkbox .p-checkbox-icon {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.p-multiselect-header .p-multiselect-header-label {
  color: #1e293b !important;
  font-weight: 700 !important;
  margin-left: 0.75rem !important;
  font-size: 0.95rem !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.dark .p-multiselect-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--surface)) !important;
  border-bottom: 2px solid var(--border) !important;
  color: var(--text-primary) !important;
  padding: 1rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.dark .p-multiselect-header::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, var(--accent), #8b5cf6, #06b6d4) !important;
}

.dark .p-multiselect-header .p-multiselect-header-checkbox {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 2px solid var(--border) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.dark .p-multiselect-header .p-multiselect-header-checkbox:hover {
  border-color: var(--accent) !important;
  transform: scale(1.05) !important;
}

.dark .p-multiselect-header .p-multiselect-header-checkbox.p-highlight {
  background: linear-gradient(135deg, var(--accent), #8b5cf6) !important;
  border-color: var(--accent) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4) !important;
}

.dark .p-multiselect-header .p-multiselect-header-checkbox .p-checkbox-icon {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.dark .p-multiselect-header .p-multiselect-header-label {
  color: var(--text-primary) !important;
  font-weight: 700 !important;
  margin-left: 0.75rem !important;
  font-size: 0.95rem !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* تحسينات Loading و Empty States */
.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-multiselect-item-empty {
  color: #64748b !important;
  font-style: italic !important;
  text-align: center !important;
  padding: 2rem 1rem !important;
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.05), rgba(100, 116, 139, 0.1)) !important;
  border-radius: 8px !important;
  margin: 0.5rem !important;
  border: 2px dashed #cbd5e1 !important;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-multiselect-item-empty::before {
  content: "🔍" !important;
  display: block !important;
  font-size: 2rem !important;
  margin-bottom: 0.5rem !important;
  opacity: 0.5 !important;
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-multiselect-item-empty::after {
  content: "No items found" !important;
  display: block !important;
  font-size: 0.9rem !important;
  color: #64748b !important;
}

.dark .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-multiselect-item-empty {
  color: var(--text-tertiary) !important;
  font-style: italic !important;
  text-align: center !important;
  padding: 2rem 1rem !important;
  background: linear-gradient(135deg, rgba(100, 116, 139, 0.1), rgba(100, 116, 139, 0.2)) !important;
  border-radius: 8px !important;
  margin: 0.5rem !important;
  border: 2px dashed var(--border) !important;
}

.dark .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-multiselect-item-empty::before {
  content: "🔍" !important;
  display: block !important;
  font-size: 2rem !important;
  margin-bottom: 0.5rem !important;
  opacity: 0.5 !important;
}

.dark .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-multiselect-item-empty::after {
  content: "No items found" !important;
  display: block !important;
  font-size: 0.9rem !important;
  color: var(--text-tertiary) !important;
}

/* تحسينات Loading State */
.p-multiselect.p-multiselect-loading {
  position: relative !important;
  overflow: hidden !important;
}

.p-multiselect.p-multiselect-loading::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent) !important;
  animation: loading-shimmer 1.5s infinite !important;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.p-multiselect.p-multiselect-loading .p-multiselect-trigger {
  color: #3b82f6 !important;
}

.p-multiselect.p-multiselect-loading .p-multiselect-trigger .p-multiselect-trigger-icon {
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.dark .p-multiselect.p-multiselect-loading::after {
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent) !important;
}

.dark .p-multiselect.p-multiselect-loading .p-multiselect-trigger {
  color: var(--accent) !important;
}

/* إصلاح حقول البحث في الـ Dropdowns للوضع المظلم */
.dark .p-dropdown-panel .p-dropdown-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
}

.dark .p-dropdown-panel .p-dropdown-filter {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.dark .p-dropdown-panel .p-dropdown-filter:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-dropdown-panel .p-dropdown-filter::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-dropdown-panel .p-dropdown-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* إصلاح حقول البحث في الـ Multiselect للوضع المظلم */
.dark .p-multiselect-panel .p-multiselect-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
}

.dark .p-multiselect-panel .p-multiselect-filter {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.dark .p-multiselect-panel .p-multiselect-filter:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-multiselect-panel .p-multiselect-filter::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* تحسين حقول البحث في الـ Multiselect - تصميم موحد */
.p-multiselect-panel .p-multiselect-filter-container {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
  border: none !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 0.75rem !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  flex-wrap: nowrap !important;
  overflow: hidden !important;
}

.p-multiselect-panel .p-multiselect-filter-container::before {
  display: none !important;
}

/* تحسين ترتيب العناصر داخل الـ header */
.p-multiselect-panel .p-multiselect-filter-container > * {
  flex-shrink: 0 !important;
}

.p-multiselect-panel .p-multiselect-filter-container .p-multiselect-filter {
  flex: 1 !important;
  min-width: 0 !important;
  margin: 0 !important;
}

.p-multiselect-panel .p-multiselect-filter {
  background: linear-gradient(145deg, #ffffff, #f1f5f9) !important;
  border: 2px solid #cbd5e1 !important;
  color: #1e293b !important;
  border-radius: 8px !important;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  font-weight: 500 !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  position: relative !important;
}

.p-multiselect-panel .p-multiselect-filter:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
}

.p-multiselect-panel .p-multiselect-filter::placeholder {
  color: #64748b !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

.p-multiselect-panel .p-multiselect-filter-icon {
  color: #64748b !important;
  position: absolute !important;
  right: 1rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  transition: all 0.2s ease !important;
  font-size: 0.875rem !important;
  z-index: 2 !important;
  pointer-events: none !important;
}

.p-multiselect-panel .p-multiselect-filter:focus + .p-multiselect-filter-icon {
  color: #3b82f6 !important;
  transform: translateY(-50%) scale(1.1) !important;
}

/* تحسين حقول البحث في الـ Multiselect للوضع المظلم */
.dark .p-multiselect-panel .p-multiselect-filter-container {
  background: linear-gradient(135deg, var(--bg-secondary), var(--surface)) !important;
  border: none !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  flex-wrap: nowrap !important;
  overflow: hidden !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container::before {
  display: none !important;
}

.dark .p-multiselect-panel .p-multiselect-filter {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 2px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  font-weight: 500 !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  flex: 1 !important;
  min-width: 0 !important;
  margin: 0 !important;
  position: relative !important;
}

.dark .p-multiselect-panel .p-multiselect-filter:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2), 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
  background: linear-gradient(145deg, var(--bg-secondary), var(--surface)) !important;
}

.dark .p-multiselect-panel .p-multiselect-filter::placeholder {
  color: var(--text-tertiary) !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-icon {
  color: var(--text-secondary) !important;
  position: absolute !important;
  right: 1rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  transition: all 0.2s ease !important;
  font-size: 0.875rem !important;
  z-index: 2 !important;
  pointer-events: none !important;
}

.dark .p-multiselect-panel .p-multiselect-filter:focus + .p-multiselect-filter-icon {
  color: var(--accent) !important;
  transform: translateY(-50%) scale(1.1) !important;
}

/* تحسينات إضافية لحقل الفلتر */
.p-multiselect-panel .p-multiselect-filter-container {
  position: relative !important;
  overflow: hidden !important;
}

.p-multiselect-panel .p-multiselect-filter-container::after {
  content: '' !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.p-multiselect-panel .p-multiselect-filter:focus ~ .p-multiselect-filter-container::after {
  opacity: 1 !important;
}

.p-multiselect-panel .p-multiselect-filter {
  position: relative !important;
  z-index: 1 !important;
}

.p-multiselect-panel .p-multiselect-filter::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05)) !important;
  border-radius: 10px !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
  z-index: -1 !important;
}

.p-multiselect-panel .p-multiselect-filter:focus::before {
  opacity: 1 !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container::after {
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent) !important;
}

.dark .p-multiselect-panel .p-multiselect-filter::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1)) !important;
}

/* إصلاح إضافي لحقل الفلتر - التأكد من الألوان الصحيحة */
.p-multiselect-panel .p-multiselect-filter-container input[type="text"] {
  background: linear-gradient(145deg, #ffffff, #f1f5f9) !important;
  border: 2px solid #cbd5e1 !important;
  color: #1e293b !important;
  border-radius: 8px !important;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  font-weight: 500 !important;
  height: 40px !important;
  flex: 1 !important;
  min-width: 0 !important;
  margin: 0 !important;
  position: relative !important;
}

.p-multiselect-panel .p-multiselect-filter-container input[type="text"]:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
}

.p-multiselect-panel .p-multiselect-filter-container input[type="text"]::placeholder {
  color: #64748b !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container input[type="text"] {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 2px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  font-weight: 500 !important;
  height: 40px !important;
  flex: 1 !important;
  min-width: 0 !important;
  margin: 0 !important;
  position: relative !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container input[type="text"]:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2), 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
  background: linear-gradient(145deg, var(--bg-secondary), var(--surface)) !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container input[type="text"]::placeholder {
  color: var(--text-tertiary) !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

/* تحسينات إضافية للـ header الموحد */
.p-multiselect-panel .p-multiselect-filter-container {
  min-height: 56px !important;
  align-items: stretch !important;
}

.p-multiselect-panel .p-multiselect-filter-container .p-multiselect-filter-wrapper {
  display: flex !important;
  align-items: center !important;
  flex: 1 !important;
  gap: 0.5rem !important;
  min-width: 0 !important;
}

.p-multiselect-panel .p-multiselect-filter-container .p-multiselect-filter-wrapper .p-multiselect-filter {
  flex: 1 !important;
  min-width: 0 !important;
}

.p-multiselect-panel .p-multiselect-filter-container .p-multiselect-filter-wrapper .p-multiselect-filter-icon {
  flex-shrink: 0 !important;
  margin-left: 0 !important;
}

/* إصلاح ترتيب العناصر في الـ header */
.p-multiselect-panel .p-multiselect-filter-container > *:not(.p-multiselect-filter-wrapper) {
  flex-shrink: 0 !important;
  margin: 0 0.25rem !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container {
  min-height: 56px !important;
  align-items: stretch !important;
}

/* تحسينات احترافية للـ Dropdown Select */
.p-dropdown {
  background: linear-gradient(145deg, #ffffff, #f8fafc) !important;
  border: 2px solid #e2e8f0 !important;
  color: #1e293b !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-height: 48px !important;
  position: relative !important;
  overflow: hidden !important;
}

.p-dropdown::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.p-dropdown:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-1px) !important;
}

.p-dropdown:hover::before {
  opacity: 1 !important;
}

.p-dropdown:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15), 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-2px) !important;
}

.p-dropdown:focus::before {
  opacity: 1 !important;
}

.dark .p-dropdown {
  background: linear-gradient(145deg, var(--bg-secondary), var(--surface)) !important;
  border: 2px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  min-height: 48px !important;
  position: relative !important;
  overflow: hidden !important;
}

.dark .p-dropdown::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, var(--accent), #8b5cf6, #06b6d4) !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

.dark .p-dropdown:hover {
  border-color: var(--accent) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-1px) !important;
}

.dark .p-dropdown:hover::before {
  opacity: 1 !important;
}

.dark .p-dropdown:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2), 0 10px 15px -3px rgba(0, 0, 0, 0.4) !important;
  transform: translateY(-2px) !important;
}

.dark .p-dropdown:focus::before {
  opacity: 1 !important;
}

/* تحسين أنماط الـ Label والـ Placeholder للـ Dropdown */
.p-dropdown .p-dropdown-label {
  color: #1e293b !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
}

.p-dropdown .p-dropdown-label.p-placeholder {
  color: #64748b !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

.dark .p-dropdown .p-dropdown-label {
  color: var(--text-primary) !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
}

.dark .p-dropdown .p-dropdown-label.p-placeholder {
  color: var(--text-tertiary) !important;
  font-style: italic !important;
  font-weight: 400 !important;
}

/* تحسين أنماط الـ Trigger (السهم) للـ Dropdown */
.p-dropdown .p-dropdown-trigger {
  color: #64748b !important;
  transition: all 0.2s ease !important;
  padding: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  height: 48px !important;
}

.p-dropdown .p-dropdown-trigger:hover {
  color: #3b82f6 !important;
  background-color: rgba(59, 130, 246, 0.05) !important;
  border-radius: 8px !important;
}

.p-dropdown .p-dropdown-trigger .p-dropdown-trigger-icon {
  transition: transform 0.2s ease !important;
  font-size: 0.875rem !important;
}

.p-dropdown.p-dropdown-open .p-dropdown-trigger .p-dropdown-trigger-icon {
  transform: rotate(180deg) !important;
}

.dark .p-dropdown .p-dropdown-trigger {
  color: var(--text-secondary) !important;
  transition: all 0.2s ease !important;
  padding: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 48px !important;
  height: 48px !important;
}

.dark .p-dropdown .p-dropdown-trigger:hover {
  color: var(--accent) !important;
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 8px !important;
}

.dark .p-dropdown .p-dropdown-trigger .p-dropdown-trigger-icon {
  transition: transform 0.2s ease !important;
  font-size: 0.875rem !important;
}

.dark .p-dropdown.p-dropdown-open .p-dropdown-trigger .p-dropdown-trigger-icon {
  transform: rotate(180deg) !important;
}

/* تحسين أنماط الـ Panel للـ Dropdown */
.p-dropdown-panel {
  background: linear-gradient(145deg, #ffffff, #f8fafc) !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  backdrop-filter: blur(20px) !important;
  overflow: hidden !important;
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin-top: 12px !important;
}

.dark .p-dropdown-panel {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(20px) !important;
  overflow: hidden !important;
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin-top: 12px !important;
}

/* تحسين أنماط العناصر في قائمة الـ Dropdown */
.p-dropdown-panel .p-dropdown-items {
  background: transparent !important;
  padding: 0.5rem 0 !important;
}

.p-dropdown-panel .p-dropdown-item {
  color: #1e293b !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0.75rem 1rem !important;
  margin: 0.125rem 0.5rem !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.p-dropdown-panel .p-dropdown-item::before {
  content: '' !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  width: 3px !important;
  background: linear-gradient(180deg, #3b82f6, #8b5cf6) !important;
  transform: scaleY(0) !important;
  transition: transform 0.2s ease !important;
}

.p-dropdown-panel .p-dropdown-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1)) !important;
  color: #3b82f6 !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

.p-dropdown-panel .p-dropdown-item:hover::before {
  transform: scaleY(1) !important;
}

.p-dropdown-panel .p-dropdown-item.p-highlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  color: white !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.p-dropdown-panel .p-dropdown-item.p-highlight::before {
  transform: scaleY(1) !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

.dark .p-dropdown-panel .p-dropdown-items {
  background: transparent !important;
  padding: 0.5rem 0 !important;
}

.dark .p-dropdown-panel .p-dropdown-item {
  color: var(--text-primary) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  padding: 0.75rem 1rem !important;
  margin: 0.125rem 0.5rem !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 0.9rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.dark .p-dropdown-panel .p-dropdown-item::before {
  content: '' !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  width: 3px !important;
  background: linear-gradient(180deg, var(--accent), #8b5cf6) !important;
  transform: scaleY(0) !important;
  transition: transform 0.2s ease !important;
}

.dark .p-dropdown-panel .p-dropdown-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15)) !important;
  color: var(--accent) !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
}

.dark .p-dropdown-panel .p-dropdown-item:hover::before {
  transform: scaleY(1) !important;
}

.dark .p-dropdown-panel .p-dropdown-item.p-highlight {
  background: linear-gradient(135deg, var(--accent), #8b5cf6) !important;
  color: white !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

.dark .p-dropdown-panel .p-dropdown-item.p-highlight::before {
  transform: scaleY(1) !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

/* إصلاح حقول البحث في الـ AutoComplete للوضع المظلم */
.dark .p-autocomplete-panel .p-autocomplete-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
}

.dark .p-autocomplete-panel .p-autocomplete-filter {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.dark .p-autocomplete-panel .p-autocomplete-filter:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-autocomplete-panel .p-autocomplete-filter::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-autocomplete-panel .p-autocomplete-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* إصلاح حقول البحث في الـ ListBox للوضع المظلم */
.dark .p-listbox-panel .p-listbox-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
}

.dark .p-listbox-panel .p-listbox-filter {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.dark .p-listbox-panel .p-listbox-filter:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-listbox-panel .p-listbox-filter::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-listbox-panel .p-listbox-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* إصلاح حقول البحث في الـ TreeSelect للوضع المظلم */
.dark .p-treeselect-panel .p-treeselect-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
}

.dark .p-treeselect-panel .p-treeselect-filter {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.dark .p-treeselect-panel .p-treeselect-filter:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-treeselect-panel .p-treeselect-filter::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-treeselect-panel .p-treeselect-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* إصلاح إضافي لحقول البحث - قواعد أكثر تحديداً */
.dark .p-dropdown-panel .p-dropdown-filter-container .p-inputtext {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-dropdown-panel .p-dropdown-filter-container input {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container .p-inputtext {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-multiselect-panel .p-multiselect-filter-container input {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-autocomplete-panel .p-autocomplete-filter-container .p-inputtext {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-autocomplete-panel .p-autocomplete-filter-container input {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-listbox-panel .p-listbox-filter-container .p-inputtext {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-listbox-panel .p-listbox-filter-container input {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-treeselect-panel .p-treeselect-filter-container .p-inputtext {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-treeselect-panel .p-treeselect-filter-container input {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح شامل لجميع حقول البحث في الـ Panels */
.dark .p-panel .p-inputtext,
.dark .p-panel input[type="text"],
.dark .p-panel input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .p-dropdown-panel input,
.dark .p-multiselect-panel input,
.dark .p-autocomplete-panel input,
.dark .p-listbox-panel input,
.dark .p-treeselect-panel input {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث العامة */
.dark input[type="text"]:not(.p-inputtext),
.dark input[type="search"]:not(.p-inputtext) {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Overlay Panels */
.dark .p-overlaypanel .p-inputtext,
.dark .p-overlaypanel input[type="text"],
.dark .p-overlaypanel input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Context Menu */
.dark .p-contextmenu .p-inputtext,
.dark .p-contextmenu input[type="text"],
.dark .p-contextmenu input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Tiered Menu */
.dark .p-tieredmenu .p-inputtext,
.dark .p-tieredmenu input[type="text"],
.dark .p-tieredmenu input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Mega Menu */
.dark .p-megamenu .p-inputtext,
.dark .p-megamenu input[type="text"],
.dark .p-megamenu input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Menubar */
.dark .p-menubar .p-inputtext,
.dark .p-menubar input[type="text"],
.dark .p-menubar input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ TabMenu */
.dark .p-tabmenu .p-inputtext,
.dark .p-tabmenu input[type="text"],
.dark .p-tabmenu input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Steps */
.dark .p-steps .p-inputtext,
.dark .p-steps input[type="text"],
.dark .p-steps input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Breadcrumb */
.dark .p-breadcrumb .p-inputtext,
.dark .p-breadcrumb input[type="text"],
.dark .p-breadcrumb input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Paginator */
.dark .p-paginator .p-inputtext,
.dark .p-paginator input[type="text"],
.dark .p-paginator input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ DataTable */
.dark .p-datatable .p-inputtext,
.dark .p-datatable input[type="text"],
.dark .p-datatable input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ TreeTable */
.dark .p-treetable .p-inputtext,
.dark .p-treetable input[type="text"],
.dark .p-treetable input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ DataView */
.dark .p-dataview .p-inputtext,
.dark .p-dataview input[type="text"],
.dark .p-dataview input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ OrderList */
.dark .p-orderlist .p-inputtext,
.dark .p-orderlist input[type="text"],
.dark .p-orderlist input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ PickList */
.dark .p-picklist .p-inputtext,
.dark .p-picklist input[type="text"],
.dark .p-picklist input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Tree */
.dark .p-tree .p-inputtext,
.dark .p-tree input[type="text"],
.dark .p-tree input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Organization Chart */
.dark .p-organizationchart .p-inputtext,
.dark .p-organizationchart input[type="text"],
.dark .p-organizationchart input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Timeline */
.dark .p-timeline .p-inputtext,
.dark .p-timeline input[type="text"],
.dark .p-timeline input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Galleria */
.dark .p-galleria .p-inputtext,
.dark .p-galleria input[type="text"],
.dark .p-galleria input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Image */
.dark .p-image .p-inputtext,
.dark .p-image input[type="text"],
.dark .p-image input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Carousel */
.dark .p-carousel .p-inputtext,
.dark .p-carousel input[type="text"],
.dark .p-carousel input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Splitter */
.dark .p-splitter .p-inputtext,
.dark .p-splitter input[type="text"],
.dark .p-splitter input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Accordion */
.dark .p-accordion .p-inputtext,
.dark .p-accordion input[type="text"],
.dark .p-accordion input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Card */
.dark .p-card .p-inputtext,
.dark .p-card input[type="text"],
.dark .p-card input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Fieldset */
.dark .p-fieldset .p-inputtext,
.dark .p-fieldset input[type="text"],
.dark .p-fieldset input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Panel */
.dark .p-panel .p-inputtext,
.dark .p-panel input[type="text"],
.dark .p-panel input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ ScrollPanel */
.dark .p-scrollpanel .p-inputtext,
.dark .p-scrollpanel input[type="text"],
.dark .p-scrollpanel input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ TabView */
.dark .p-tabview .p-inputtext,
.dark .p-tabview input[type="text"],
.dark .p-tabview input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Toolbar */
.dark .p-toolbar .p-inputtext,
.dark .p-toolbar input[type="text"],
.dark .p-toolbar input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ SplitButton */
.dark .p-splitbutton .p-inputtext,
.dark .p-splitbutton input[type="text"],
.dark .p-splitbutton input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ ToggleButton */
.dark .p-togglebutton .p-inputtext,
.dark .p-togglebutton input[type="text"],
.dark .p-togglebutton input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ SelectButton */
.dark .p-selectbutton .p-inputtext,
.dark .p-selectbutton input[type="text"],
.dark .p-selectbutton input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Rating */
.dark .p-rating .p-inputtext,
.dark .p-rating input[type="text"],
.dark .p-rating input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Slider */
.dark .p-slider .p-inputtext,
.dark .p-slider input[type="text"],
.dark .p-slider input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Knob */
.dark .p-knob .p-inputtext,
.dark .p-knob input[type="text"],
.dark .p-knob input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ ColorPicker */
.dark .p-colorpicker .p-inputtext,
.dark .p-colorpicker input[type="text"],
.dark .p-colorpicker input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ CascadeSelect */
.dark .p-cascadeselect .p-inputtext,
.dark .p-cascadeselect input[type="text"],
.dark .p-cascadeselect input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Chips */
.dark .p-chips .p-inputtext,
.dark .p-chips input[type="text"],
.dark .p-chips input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputMask */
.dark .p-inputmask .p-inputtext,
.dark .p-inputmask input[type="text"],
.dark .p-inputmask input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ KeyFilter */
.dark .p-keyfilter .p-inputtext,
.dark .p-keyfilter input[type="text"],
.dark .p-keyfilter input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputNumber */
.dark .p-inputnumber .p-inputtext,
.dark .p-inputnumber input[type="text"],
.dark .p-inputnumber input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputSwitch */
.dark .p-inputswitch .p-inputtext,
.dark .p-inputswitch input[type="text"],
.dark .p-inputswitch input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputTextarea */
.dark .p-inputtextarea .p-inputtext,
.dark .p-inputtextarea input[type="text"],
.dark .p-inputtextarea input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Editor */
.dark .p-editor .p-inputtext,
.dark .p-editor input[type="text"],
.dark .p-editor input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Textarea */
.dark .p-textarea .p-inputtext,
.dark .p-textarea input[type="text"],
.dark .p-textarea input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ TriStateCheckbox */
.dark .p-tristatecheckbox .p-inputtext,
.dark .p-tristatecheckbox input[type="text"],
.dark .p-tristatecheckbox input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ ToggleButton */
.dark .p-togglebutton .p-inputtext,
.dark .p-togglebutton input[type="text"],
.dark .p-togglebutton input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ SelectButton */
.dark .p-selectbutton .p-inputtext,
.dark .p-selectbutton input[type="text"],
.dark .p-selectbutton input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Rating */
.dark .p-rating .p-inputtext,
.dark .p-rating input[type="text"],
.dark .p-rating input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Slider */
.dark .p-slider .p-inputtext,
.dark .p-slider input[type="text"],
.dark .p-slider input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Knob */
.dark .p-knob .p-inputtext,
.dark .p-knob input[type="text"],
.dark .p-knob input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ ColorPicker */
.dark .p-colorpicker .p-inputtext,
.dark .p-colorpicker input[type="text"],
.dark .p-colorpicker input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ CascadeSelect */
.dark .p-cascadeselect .p-inputtext,
.dark .p-cascadeselect input[type="text"],
.dark .p-cascadeselect input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Chips */
.dark .p-chips .p-inputtext,
.dark .p-chips input[type="text"],
.dark .p-chips input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputMask */
.dark .p-inputmask .p-inputtext,
.dark .p-inputmask input[type="text"],
.dark .p-inputmask input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ KeyFilter */
.dark .p-keyfilter .p-inputtext,
.dark .p-keyfilter input[type="text"],
.dark .p-keyfilter input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputNumber */
.dark .p-inputnumber .p-inputtext,
.dark .p-inputnumber input[type="text"],
.dark .p-inputnumber input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputSwitch */
.dark .p-inputswitch .p-inputtext,
.dark .p-inputswitch input[type="text"],
.dark .p-inputswitch input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ InputTextarea */
.dark .p-inputtextarea .p-inputtext,
.dark .p-inputtextarea input[type="text"],
.dark .p-inputtextarea input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Editor */
.dark .p-editor .p-inputtext,
.dark .p-editor input[type="text"],
.dark .p-editor input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ Textarea */
.dark .p-textarea .p-inputtext,
.dark .p-textarea input[type="text"],
.dark .p-textarea input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح حقول البحث في الـ TriStateCheckbox */
.dark .p-tristatecheckbox .p-inputtext,
.dark .p-tristatecheckbox input[type="text"],
.dark .p-tristatecheckbox input[type="search"] {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

/* إصلاح شامل لجميع حقول الإدخال في الوضع المظلم */
.dark input:not([type="checkbox"]):not([type="radio"]):not([type="file"]):not([type="submit"]):not([type="button"]):not([type="reset"]) {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
}

.dark input:not([type="checkbox"]):not([type="radio"]):not([type="file"]):not([type="submit"]):not([type="button"]):not([type="reset"]):focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark input:not([type="checkbox"]):not([type="radio"]):not([type="file"]):not([type="submit"]):not([type="button"]):not([type="reset"])::placeholder {
  color: var(--text-tertiary) !important;
}

/* إصلاح خاص لحاوية البحث في قائمة اختيار البلدان */
.dark .p-dropdown-panel .p-dropdown-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
  border-radius: 12px 12px 0 0 !important;
}

.dark .p-dropdown-panel .p-dropdown-filter-container .p-inputtext {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.dark .p-dropdown-panel .p-dropdown-filter-container .p-inputtext:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-dropdown-panel .p-dropdown-filter-container .p-inputtext::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-dropdown-panel .p-dropdown-filter-container .p-dropdown-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* إصلاح شامل لجميع حاويات البحث في الـ Dropdowns */
.dark .p-dropdown-panel .p-dropdown-filter-container,
.dark .p-multiselect-panel .p-multiselect-filter-container,
.dark .p-autocomplete-panel .p-autocomplete-filter-container,
.dark .p-listbox-panel .p-listbox-filter-container,
.dark .p-treeselect-panel .p-treeselect-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
}

/* إصلاح خاص للـ Dropdown Panel */
.dark .p-dropdown-panel {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .p-dropdown-panel .p-dropdown-items {
  background: transparent !important;
}

.dark .p-dropdown-panel .p-dropdown-item {
  color: var(--text-primary) !important;
  transition: all 0.2s ease !important;
  padding: 0.75rem 1rem !important;
}

.dark .p-dropdown-panel .p-dropdown-item:hover {
  background-color: var(--surface-hover) !important;
  color: var(--accent) !important;
}

.dark .p-dropdown-panel .p-dropdown-item.p-highlight {
  background-color: var(--accent) !important;
  color: white !important;
}

/* إصلاح خاص للـ Dropdown Panel SM */
.dark .p-dropdown-panel-sm {
  background: linear-gradient(145deg, var(--surface), var(--bg-secondary)) !important;
  border: 1px solid var(--border) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(20px) !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-filter-container {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  padding: 0.75rem !important;
  border-radius: 12px 12px 0 0 !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-filter-container .p-inputtext {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-filter-container .p-inputtext:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-filter-container .p-inputtext::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-filter-container .p-dropdown-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-items {
  background: transparent !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-item {
  color: var(--text-primary) !important;
  transition: all 0.2s ease !important;
  padding: 0.75rem 1rem !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-item:hover {
  background-color: var(--surface-hover) !important;
  color: var(--accent) !important;
}

.dark .p-dropdown-panel-sm .p-dropdown-item.p-highlight {
  background-color: var(--accent) !important;
  color: white !important;
}

/* إصلاح الـ p-dropdown-header للوضع المظلم - تصميم محسن */
.dark .p-dropdown-header {
  background-color: var(--bg-secondary) !important;
  border-bottom: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  padding: 0.5rem !important;
  margin: 0 !important;
}

.dark .p-dropdown-header .p-dropdown-filter-container {
  background-color: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.dark .p-dropdown-header .p-dropdown-filter {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  margin: 0 !important;
}

.dark .p-dropdown-header .p-dropdown-filter:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.dark .p-dropdown-header .p-dropdown-filter::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .p-dropdown-header .p-dropdown-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.5rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 1rem !important;
  height: 1rem !important;
  font-size: 0.875rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* تحسين موقع أيقونة البحث في جميع الـ Dropdowns */
.dark .p-dropdown-panel .p-dropdown-filter-icon,
.dark .p-dropdown-panel-sm .p-dropdown-filter-icon,
.dark .p-multiselect-panel .p-multiselect-filter-icon,
.dark .p-autocomplete-panel .p-autocomplete-filter-icon,
.dark .p-listbox-panel .p-listbox-filter-icon,
.dark .p-treeselect-panel .p-treeselect-filter-icon {
  color: var(--text-secondary) !important;
  right: 0.5rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 1rem !important;
  height: 1rem !important;
  font-size: 0.875rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

/* Dark Mode for Custom Components */
.dark .professional-library-section .image-container {
  background-color: var(--surface);
}

/* Dark Mode for Rope Segment Inside */
.dark [data-dark-style] {
  background: linear-gradient(135deg, #334155 0%, #475569 50%, #64748b 100%) !important;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.3), 0 1px 2px rgba(255,255,255,0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dark .professional-library-section .image-item {
  border-color: var(--border);
}

.dark .professional-library-section .image-item:hover {
  border-color: var(--accent);
}

/* Dark Mode Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Dark Mode for Main Containers Only */
.dark {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Main Layout Containers */
.dark .main-container,
.dark .page-container,
.dark .dashboard-container,
.dark .content-container,
.dark .app-container,
.dark .layout-container {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Main Content Areas */
.dark .main-content,
.dark .page-content,
.dark .dashboard-content,
.dark .content-area,
.dark .app-content {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Main Sections */
.dark .main-section,
.dark .page-section,
.dark .dashboard-section,
.dark .content-section {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* إصلاح الحاوية الرئيسية للوضع المظلم بدون hover */
.dark .groups-cards-management {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .groups-cards-management.border-t {
  border-top-color: var(--border) !important;
}

.dark .groups-cards-management.pt-6 {
  padding-top: 1.5rem !important;
}

.dark .groups-cards-management.mt-6 {
  margin-top: 1.5rem !important;
}

.dark .groups-cards-management.border-gray-200 {
  border-color: var(--border) !important;
}

.dark .groups-cards-management.mt-6.border-t.border-gray-200.pt-6 {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

/* إصلاح hover للكروت فقط في Group Cards Management */
.dark .groups-cards-management .bg-white {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .bg-white:hover {
  background-color: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت الخضراء */
.dark .groups-cards-management .border-green-200 {
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .groups-cards-management .border-green-200:hover {
  border-color: rgba(34, 197, 94, 0.3) !important;
  background-color: var(--surface-hover) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت الزرقاء */
.dark .groups-cards-management .border-blue-200 {
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .groups-cards-management .border-blue-200:hover {
  border-color: rgba(59, 130, 246, 0.3) !important;
  background-color: var(--surface-hover) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت الفردية مع Framer Motion */
.dark .groups-cards-management .group.relative.bg-white.rounded-lg {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg:hover {
  background-color: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت مع cursor-pointer */
.dark .groups-cards-management .cursor-pointer {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .cursor-pointer:hover {
  background-color: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت مع motion.div */
.dark .groups-cards-management .motion-div {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .motion-div:hover {
  background-color: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إزالة hover من الحاويات الكبيرة - Available Cards و Group Assigned Cards */
.dark .groups-cards-management .bg-gradient-to-br.from-green-50.via-emerald-50.to-green-100.rounded-xl.p-4.border.border-green-200.shadow-sm:hover {
  background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transform: none !important;
}

.dark .groups-cards-management .bg-gradient-to-br.from-blue-50.via-indigo-50.to-blue-100.rounded-xl.p-4.border.border-blue-200.shadow-sm:hover {
  background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1), rgba(59, 130, 246, 0.1)) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transform: none !important;
}

/* إزالة hover من الحاويات الكبيرة مع motion.div */
.dark .groups-cards-management .motion-div.bg-gradient-to-br.from-green-50.via-emerald-50.to-green-100:hover {
  background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transform: none !important;
}

.dark .groups-cards-management .motion-div.bg-gradient-to-br.from-blue-50.via-indigo-50.to-blue-100:hover {
  background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1), rgba(59, 130, 246, 0.1)) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  transform: none !important;
}

/* إصلاح قوي للـ hover مع Framer Motion - الكروت الفردية */
.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200 {
  background-color: var(--surface) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200:hover {
  background-color: var(--surface-hover) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-blue-200 {
  background-color: var(--surface) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-blue-200:hover {
  background-color: var(--surface-hover) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت مع cursor-pointer */
.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200.cursor-pointer {
  background-color: var(--surface) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200.cursor-pointer:hover {
  background-color: var(--surface-hover) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت مع transition-all */
.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200.transition-all {
  background-color: var(--surface) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200.transition-all:hover {
  background-color: var(--surface-hover) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-blue-200.transition-all {
  background-color: var(--surface) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-blue-200.transition-all:hover {
  background-color: var(--surface-hover) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح حاوية No cards found للوضع المظلم */
.dark .groups-cards-management .bg-gradient-to-br.from-gray-50.to-gray-100 {
  background: linear-gradient(to bottom right, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-color: var(--border) !important;
}

.dark .groups-cards-management .border-2.border-dashed.border-gray-300 {
  border-color: var(--border) !important;
}

.dark .groups-cards-management .bg-gray-200 {
  background-color: var(--bg-secondary) !important;
}

.dark .groups-cards-management .text-gray-500 {
  color: var(--text-secondary) !important;
}

.dark .groups-cards-management .text-gray-600 {
  color: var(--text-primary) !important;
}

/* إصلاح زر الإغلاق والفتح للوضع المظلم */
.dark .bg-gradient-to-r.from-gray-50.to-gray-100 {
  background: linear-gradient(to right, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-color: var(--border) !important;
}

.dark .bg-gradient-to-r.from-gray-50.to-gray-100:hover {
  background: linear-gradient(to right, var(--surface-hover), var(--surface)) !important;
  border-color: var(--accent) !important;
}

.dark .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .group-hover\:text-blue-600:hover {
  color: var(--accent) !important;
}

/* إصلاح العناوين والنصوص */
.dark .text-gray-800 {
  color: var(--text-primary) !important;
}

.dark .text-gray-500 {
  color: var(--text-secondary) !important;
}

/* إصلاح أيقونة الـ Credit Card */
.dark .bg-gradient-to-br.from-blue-500.to-indigo-600 {
  background: linear-gradient(to bottom right, var(--accent), var(--accent-hover)) !important;
}

/* إصلاح حاوية Available Cards للوضع المظلم */
.dark .bg-gradient-to-br.from-green-50.via-emerald-50.to-green-100 {
  background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.dark .bg-gradient-to-br.from-green-50.via-emerald-50.to-green-100 .text-green-800 {
  color: #22c55e !important;
}

.dark .bg-gradient-to-br.from-green-50.via-emerald-50.to-green-100 .bg-green-500 {
  background-color: #22c55e !important;
}

/* إصلاح حاوية Group Assigned Cards للوضع المظلم */
.dark .bg-gradient-to-br.from-blue-50.via-indigo-50.to-blue-100 {
  background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1), rgba(59, 130, 246, 0.1)) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .bg-gradient-to-br.from-blue-50.via-indigo-50.to-blue-100 .text-blue-800 {
  color: var(--accent) !important;
}

.dark .bg-gradient-to-br.from-blue-50.via-indigo-50.to-blue-100 .bg-blue-500 {
  background-color: var(--accent) !important;
}

/* إصلاح حاوية No cards found للوضع المظلم */
.dark .bg-gradient-to-br.from-gray-50.to-gray-100 {
  background: linear-gradient(to bottom right, var(--bg-secondary), var(--bg-tertiary)) !important;
  border-color: var(--border) !important;
}

.dark .bg-gradient-to-br.from-gray-50.to-gray-100 .text-gray-600 {
  color: var(--text-primary) !important;
}

.dark .bg-gradient-to-br.from-gray-50.to-gray-100 .text-gray-500 {
  color: var(--text-secondary) !important;
}

.dark .bg-gradient-to-br.from-gray-50.to-gray-100 .bg-gray-200 {
  background-color: var(--bg-secondary) !important;
}

/* إصلاح الخط الفاصل للوضع المظلم */
.dark .border-t.border-gray-200 {
  border-top-color: var(--border) !important;
}


/* إصلاح شامل للـ hover مع Framer Motion - جميع الكروت */
.dark .groups-cards-management [data-framer-motion] {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management [data-framer-motion]:hover {
  background-color: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

/* إصلاح hover للكروت مع جميع الكلاسات الممكنة */
.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200.hover\:border-green-300.hover\:shadow-lg.transition-all.duration-200.cursor-pointer {
  background-color: var(--surface) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-green-200.hover\:border-green-300.hover\:shadow-lg.transition-all.duration-200.cursor-pointer:hover {
  background-color: var(--surface-hover) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-blue-200.hover\:border-blue-300.hover\:shadow-lg.transition-all.duration-200 {
  background-color: var(--surface) !important;
  border-color: rgba(59, 130, 246, 0.2) !important;
  transition: all 0.2s ease !important;
}

.dark .groups-cards-management .group.relative.bg-white.rounded-lg.p-3.border.border-blue-200.hover\:border-blue-300.hover\:shadow-lg.transition-all.duration-200:hover {
  background-color: var(--surface-hover) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
  transform: scale(1.01) translateY(-1px) !important;
}


/* Main Wrappers */
.dark .main-wrapper,
.dark .page-wrapper,
.dark .dashboard-wrapper,
.dark .content-wrapper,
.dark .app-wrapper {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Main Views */
.dark .main-view,
.dark .page-view,
.dark .dashboard-view,
.dark .content-view {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Main Headers and Footers */
.dark .main-header,
.dark .page-header,
.dark .dashboard-header,
.dark .content-header,
.dark .app-header {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .main-footer,
.dark .page-footer,
.dark .dashboard-footer,
.dark .content-footer,
.dark .app-footer {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Main Navigation */
.dark .main-nav,
.dark .page-nav,
.dark .dashboard-nav,
.dark .content-nav,
.dark .app-nav {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Main Sidebars */
.dark .main-sidebar,
.dark .page-sidebar,
.dark .dashboard-sidebar,
.dark .content-sidebar,
.dark .app-sidebar {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Main Cards and Panels (Large ones only) */
.dark .main-card,
.dark .page-card,
.dark .dashboard-card,
.dark .content-card,
.dark .app-card,
.dark .main-panel,
.dark .page-panel,
.dark .dashboard-panel,
.dark .content-panel,
.dark .app-panel {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Main Forms (Large forms only) */
.dark .main-form,
.dark .page-form,
.dark .dashboard-form,
.dark .content-form,
.dark .app-form {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Main Tables (Large tables only) */
.dark .main-table,
.dark .page-table,
.dark .dashboard-table,
.dark .content-table,
.dark .app-table {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Main Modals and Dialogs */
.dark .main-modal,
.dark .page-modal,
.dark .dashboard-modal,
.dark .content-modal,
.dark .app-modal,
.dark .main-dialog,
.dark .page-dialog,
.dark .dashboard-dialog,
.dark .content-dialog,
.dark .app-dialog {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

/* Main Text Elements */
.dark .main-title,
.dark .page-title,
.dark .dashboard-title,
.dark .content-title,
.dark .app-title {
  color: var(--text-primary) !important;
}

.dark .main-subtitle,
.dark .page-subtitle,
.dark .dashboard-subtitle,
.dark .content-subtitle,
.dark .app-subtitle {
  color: var(--text-secondary) !important;
}

.dark .main-text,
.dark .page-text,
.dark .dashboard-text,
.dark .content-text,
.dark .app-text {
  color: var(--text-primary) !important;
}

/* Main Links */
.dark .main-link,
.dark .page-link,
.dark .dashboard-link,
.dark .content-link,
.dark .app-link {
  color: var(--accent) !important;
}

.dark .main-link:hover,
.dark .page-link:hover,
.dark .dashboard-link:hover,
.dark .content-link:hover,
.dark .app-link:hover {
  color: var(--accent-hover) !important;
}

/* Dark Mode Toggle Button Styles */
.dark-mode-toggle {
  position: relative;
  width: 60px;
  height: 30px;
  background: linear-gradient(45deg, #1e293b, #334155);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid var(--border);
  overflow: hidden;
}

.dark-mode-toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background: linear-gradient(45deg, #fbbf24, #f59e0b);
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark-mode-toggle.active {
  background: linear-gradient(45deg, #1e40af, #3b82f6);
  border-color: var(--accent);
}

.dark-mode-toggle.active::before {
  transform: translateX(30px);
  background: linear-gradient(45deg, #1e40af, #3b82f6);
}

.dark-mode-toggle:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.dark-mode-toggle:active {
  transform: scale(0.95);
}

/* Glow effect for dark mode toggle */
.dark-mode-toggle.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Dark Mode Icons */
.dark-mode-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  transition: all 0.3s ease;
  z-index: 1;
}

.dark-mode-icon.sun {
  left: 6px;
  color: #fbbf24;
}

.dark-mode-icon.moon {
  right: 6px;
  color: #3b82f6;
}

.dark-mode-toggle.active .dark-mode-icon.sun {
  opacity: 0;
  transform: translateY(-50%) scale(0);
}

.dark-mode-toggle.active .dark-mode-icon.moon {
  opacity: 1;
  transform: translateY(-50%) scale(1);
}

.dark-mode-toggle:not(.active) .dark-mode-icon.sun {
  opacity: 1;
  transform: translateY(-50%) scale(1);
}

.dark-mode-toggle:not(.active) .dark-mode-icon.moon {
  opacity: 0;
  transform: translateY(-50%) scale(0);
}