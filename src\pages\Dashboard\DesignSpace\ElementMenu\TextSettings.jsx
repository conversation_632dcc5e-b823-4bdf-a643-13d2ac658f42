import { useState, useEffect, useCallback } from "react";
import { useDarkMode } from '@contexts/DarkModeContext';

import { LuCaseLower, LuCaseSensitive, LuCaseUpper } from "react-icons/lu";
import { FaBold, FaItalic, FaUnderline, FaRegStar, FaTrash } from "react-icons/fa";
import { IoPricetagsOutline } from "react-icons/io5";
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Slider } from 'primereact/slider';

import { alignmentsOptions, colorsOptions } from "@constants";
import { useDesignSpace } from "@contexts/DesignSpaceContext";
import FieldsDropdown from "./FieldsDropdown";

// خيارات الخطوط والخصائص النصية من AIToolsHub
const aiFontFamilyOptions = [
  // خطوط عربية أساسية مدعومة من Google Fonts
  { label: 'Cairo (Modern)', value: 'Cairo, Arial, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Tajawal (Clean)', value: 'Tajawal, Arial, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Amiri (Classic)', value: 'Amiri, serif', example: 'مرحبا بالعالم' },
  { label: 'Noto Naskh Arabic (Traditional)', value: 'Noto Naskh Arabic, serif', example: 'مرحبا بالعالم' },
  { label: 'Noto Kufi Arabic (Geometric)', value: 'Noto Kufi Arabic, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Almarai (Bold)', value: 'Almarai, Arial, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Changa (Modern)', value: 'Changa, Arial, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Reem Kufi (Elegant)', value: 'Reem Kufi, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Markazi Text (Decorative)', value: 'Markazi Text, serif', example: 'مرحبا بالعالم' },
  { label: 'Scheherazade New (Story)', value: 'Scheherazade New, serif', example: 'مرحبا بالعالم' },
  { label: 'Lateef (Poetry)', value: 'Lateef, serif', example: 'مرحبا بالعالم' },
  { label: 'Harmattan (Desert)', value: 'Harmattan, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'El Messiri (Egyptian)', value: 'El Messiri, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Aref Ruqaa (Calligraphy)', value: 'Aref Ruqaa, serif', example: 'مرحبا بالعالم' },
  { label: 'IBM Plex Sans Arabic (Tech)', value: 'IBM Plex Sans Arabic, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Noto Sans Arabic (Clean)', value: 'Noto Sans Arabic, sans-serif', example: 'مرحبا بالعالم' },
  { label: 'Noto Serif Arabic (Elegant)', value: 'Noto Serif Arabic, serif', example: 'مرحبا بالعالم' },
  // خطوط إنجليزية أساسية مدعومة من Google Fonts
  { label: 'Roboto (Modern)', value: 'Roboto, Arial, sans-serif', example: 'Hello World' },
  { label: 'Playfair Display (Elegant)', value: 'Playfair Display, Georgia, serif', example: 'Hello World' },
  { label: 'Pacifico (Handwriting)', value: 'Pacifico, cursive', example: 'Hello World' },
  { label: 'Bebas Neue (Bold)', value: 'Bebas Neue, Impact, sans-serif', example: 'Hello World' },
  { label: 'Dancing Script (Cursive)', value: 'Dancing Script, cursive', example: 'Hello World' },
  { label: 'Orbitron (Futuristic)', value: 'Orbitron, monospace', example: 'Hello World' },
  { label: 'Lobster (Fun)', value: 'Lobster, cursive', example: 'Hello World' },
  { label: 'Abril Fatface (Decorative)', value: 'Abril Fatface, serif', example: 'Hello World' },
  { label: 'Righteous (Bold)', value: 'Righteous, cursive', example: 'Hello World' },
  { label: 'Permanent Marker (Marker)', value: 'Permanent Marker, cursive', example: 'Hello World' },
  { label: 'Fredoka One (Rounded)', value: 'Fredoka One, cursive', example: 'Hello World' },
  { label: 'Bangers (Comic)', value: 'Bangers, cursive', example: 'Hello World' },
  { label: 'Chewy (Bubble)', value: 'Chewy, cursive', example: 'Hello World' },
  { label: 'Kaushan Script (Signature)', value: 'Kaushan Script, cursive', example: 'Hello World' },
  { label: 'Satisfy (Handwriting)', value: 'Satisfy, cursive', example: 'Hello World' },
  { label: 'Great Vibes (Elegant)', value: 'Great Vibes, cursive', example: 'Hello World' },
  { label: 'Cinzel (Classic)', value: 'Cinzel, serif', example: 'Hello World' },
  { label: 'UnifrakturMaguntia (Gothic)', value: 'UnifrakturMaguntia, cursive', example: 'Hello World' },
  { label: 'Creepster (Horror)', value: 'Creepster, cursive', example: 'Hello World' },
  { label: 'Faster One (Speed)', value: 'Faster One, cursive', example: 'Hello World' },
  { label: 'Press Start 2P (Retro)', value: 'Press Start 2P, cursive', example: 'Hello World' },
  { label: 'VT323 (Terminal)', value: 'VT323, monospace', example: 'Hello World' },
  { label: 'Share Tech Mono (Tech)', value: 'Share Tech Mono, monospace', example: 'Hello World' },
  { label: 'Space Mono (Space)', value: 'Space Mono, monospace', example: 'Hello World' },
  { label: 'Major Mono Display (Minimal)', value: 'Major Mono Display, monospace', example: 'Hello World' },
  { label: 'Crimson Text (Serif)', value: 'Crimson Text, serif', example: 'Hello World' },
  { label: 'Libre Baskerville (Classic)', value: 'Libre Baskerville, serif', example: 'Hello World' },
  { label: 'Lora (Elegant Serif)', value: 'Lora, serif', example: 'Hello World' },
  { label: 'Source Sans Pro (Clean)', value: 'Source Sans Pro, Arial, sans-serif', example: 'Hello World' },
  { label: 'Nunito (Rounded)', value: 'Nunito, Arial, sans-serif', example: 'Hello World' },
  { label: 'Quicksand (Smooth)', value: 'Quicksand, Arial, sans-serif', example: 'Hello World' },
  { label: 'Comfortaa (Comfortable)', value: 'Comfortaa, Arial, sans-serif', example: 'Hello World' },
  { label: 'Varela Round (Circular)', value: 'Varela Round, Arial, sans-serif', example: 'Hello World' },
  { label: 'Maven Pro (Modern)', value: 'Maven Pro, Arial, sans-serif', example: 'Hello World' },
  { label: 'Exo 2 (Futuristic)', value: 'Exo 2, Arial, sans-serif', example: 'Hello World' },
  { label: 'Rajdhani (Tech)', value: 'Rajdhani, Arial, sans-serif', example: 'Hello World' },
  { label: 'Titillium Web (Clean)', value: 'Titillium Web, Arial, sans-serif', example: 'Hello World' },
  { label: 'Josefin Sans (Thin)', value: 'Josefin Sans, Arial, sans-serif', example: 'Hello World' },
  { label: 'Abel (Minimal)', value: 'Abel, Arial, sans-serif', example: 'Hello World' },
  { label: 'Anton (Bold)', value: 'Anton, Impact, sans-serif', example: 'Hello World' },
  { label: 'Bungee (Heavy)', value: 'Bungee, Impact, sans-serif', example: 'Hello World' },
  { label: 'Black Ops One (Military)', value: 'Black Ops One, cursive', example: 'Hello World' },
  { label: 'Russo One (Russian)', value: 'Russo One, sans-serif', example: 'Hello World' },
  { label: 'Audiowide (Audio)', value: 'Audiowide, cursive', example: 'Hello World' },
  { label: 'Changa One (Funky)', value: 'Changa One, cursive', example: 'Hello World' },
  { label: 'Sniglet (Comic)', value: 'Sniglet, cursive', example: 'Hello World' },
  { label: 'Boogaloo (Fun)', value: 'Boogaloo, cursive', example: 'Hello World' },
  { label: 'Bubblegum Sans (Sweet)', value: 'Bubblegum Sans, cursive', example: 'Hello World' },
  { label: 'Cherry Cream Soda (Retro)', value: 'Cherry Cream Soda, cursive', example: 'Hello World' },
  { label: 'Freckle Face (Cute)', value: 'Freckle Face, cursive', example: 'Hello World' },
  { label: 'Gloria Hallelujah (Praise)', value: 'Gloria Hallelujah, cursive', example: 'Hello World' },
  { label: 'Indie Flower (Handwritten)', value: 'Indie Flower, cursive', example: 'Hello World' },
  { label: 'Kalam (Handwriting)', value: 'Kalam, cursive', example: 'Hello World' },
  { label: 'Patrick Hand (Casual)', value: 'Patrick Hand, cursive', example: 'Hello World' },
  { label: 'Reenie Beanie (Sketchy)', value: 'Reenie Beanie, cursive', example: 'Hello World' },
  { label: 'Rock Salt (Rough)', value: 'Rock Salt, cursive', example: 'Hello World' },
  { label: 'Shadows Into Light (Shadow)', value: 'Shadows Into Light, cursive', example: 'Hello World' },
  { label: 'Special Elite (Typewriter)', value: 'Special Elite, cursive', example: 'Hello World' },
  { label: 'Walter Turncoat (Messy)', value: 'Walter Turncoat, cursive', example: 'Hello World' },
  // خطوط إنجليزية شائعة متوفرة في النظام
  { label: 'Arial (System)', value: 'Arial, sans-serif', example: 'Hello World' },
  { label: 'Helvetica (System)', value: 'Helvetica, sans-serif', example: 'Hello World' },
  { label: 'Times New Roman (System)', value: 'Times New Roman, serif', example: 'Hello World' },
  { label: 'Georgia (System)', value: 'Georgia, serif', example: 'Hello World' },
  { label: 'Verdana (System)', value: 'Verdana, sans-serif', example: 'Hello World' },
  { label: 'Courier New (System)', value: 'Courier New, monospace', example: 'Hello World' },
  { label: 'Tahoma (System)', value: 'Tahoma, sans-serif', example: 'Hello World' },
  { label: 'Trebuchet MS (System)', value: 'Trebuchet MS, sans-serif', example: 'Hello World' },
  { label: 'Impact (System)', value: 'Impact, sans-serif', example: 'Hello World' },
  { label: 'Comic Sans MS (System)', value: 'Comic Sans MS, cursive', example: 'Hello World' }
];
const aiFontWeightOptions = [
    { label: 'Normal', value: 'normal' },
    { label: 'Bold', value: 'bold' },
    { label: 'Light', value: '300' },
    { label: 'Semi-Bold', value: '600' },
    { label: 'Extra Bold', value: '800' }
];
const aiTextAlignOptions = [
    { label: 'Left', value: 'left' },
    { label: 'Center', value: 'center' },
    { label: 'Right', value: 'right' },
    { label: 'Justify', value: 'justify' }
];
const aiTextDecorationOptions = [
    { label: 'None', value: 'none' },
    { label: 'Underline', value: 'underline' },
    { label: 'Overline', value: 'overline' },
    { label: 'Line-through', value: 'line-through' }
];
const aiTextTransformOptions = [
    { label: 'None', value: 'none' },
    { label: 'Uppercase', value: 'uppercase' },
    { label: 'Lowercase', value: 'lowercase' },
    { label: 'Capitalize', value: 'capitalize' }
];
const aiTextEffectOptions = [
    { label: 'None', value: 'none' },
    { label: 'Shadow', value: 'shadow' },
    { label: 'Glow', value: 'glow' },
    { label: '3D', value: '3d' },
    { label: 'Outline', value: 'outline' },
    { label: 'Gradient', value: 'gradient' }
];

// دالة لتحميل الخطوط من Google Fonts
const loadGoogleFonts = () => {
  if (typeof document !== 'undefined') {
    const fontLinks = [
      // خطوط عربية أساسية
      'https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Noto+Naskh+Arabic:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Almarai:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Changa:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Reem+Kufi:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Markazi+Text:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Lateef:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Harmattan:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=El+Messiri:wght@400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Aref+Ruqaa:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap',
      'https://fonts.googleapis.com/css2?family=Noto+Serif+Arabic:wght@200;300;400;500;600;700;800;900&display=swap',
      // خطوط إنجليزية أساسية
      'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Pacifico&display=swap',
      'https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap',
      'https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap',
      'https://fonts.googleapis.com/css2?family=Lobster&display=swap',
      'https://fonts.googleapis.com/css2?family=Abril+Fatface&display=swap',
      'https://fonts.googleapis.com/css2?family=Righteous&display=swap',
      'https://fonts.googleapis.com/css2?family=Permanent+Marker&display=swap',
      'https://fonts.googleapis.com/css2?family=Fredoka+One&display=swap',
      'https://fonts.googleapis.com/css2?family=Bangers&display=swap',
      'https://fonts.googleapis.com/css2?family=Chewy&display=swap',
      'https://fonts.googleapis.com/css2?family=Kaushan+Script&display=swap',
      'https://fonts.googleapis.com/css2?family=Satisfy&display=swap',
      'https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap',
      'https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800;900&display=swap',
      'https://fonts.googleapis.com/css2?family=UnifrakturMaguntia&display=swap',
      'https://fonts.googleapis.com/css2?family=Creepster&display=swap',
      'https://fonts.googleapis.com/css2?family=Faster+One&display=swap',
      'https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap',
      'https://fonts.googleapis.com/css2?family=VT323&display=swap',
      'https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap',
      'https://fonts.googleapis.com/css2?family=Space+Mono:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Major+Mono+Display&display=swap',
      'https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Lora:wght@400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Varela+Round&display=swap',
      'https://fonts.googleapis.com/css2?family=Maven+Pro:wght@400;500;600;700;800;900&display=swap',
      'https://fonts.googleapis.com/css2?family=Exo+2:wght@100;200;300;400;500;600;700;800;900&display=swap',
      'https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Titillium+Web:wght@200;300;400;600;700;900&display=swap',
      'https://fonts.googleapis.com/css2?family=Josefin+Sans:wght@100;200;300;400;500;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Abel&display=swap',
      'https://fonts.googleapis.com/css2?family=Anton&display=swap',
      'https://fonts.googleapis.com/css2?family=Bungee&display=swap',
      'https://fonts.googleapis.com/css2?family=Black+Ops+One&display=swap',
      'https://fonts.googleapis.com/css2?family=Russo+One&display=swap',
      'https://fonts.googleapis.com/css2?family=Audiowide&display=swap',
      'https://fonts.googleapis.com/css2?family=Changa+One&display=swap',
      'https://fonts.googleapis.com/css2?family=Sniglet:wght@400;800&display=swap',
      'https://fonts.googleapis.com/css2?family=Boogaloo&display=swap',
      'https://fonts.googleapis.com/css2?family=Bubblegum+Sans&display=swap',
      'https://fonts.googleapis.com/css2?family=Cherry+Cream+Soda&display=swap',
      'https://fonts.googleapis.com/css2?family=Freckle+Face&display=swap',
      'https://fonts.googleapis.com/css2?family=Gloria+Hallelujah&display=swap',
      'https://fonts.googleapis.com/css2?family=Indie+Flower&display=swap',
      'https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Patrick+Hand&display=swap',
      'https://fonts.googleapis.com/css2?family=Reenie+Beanie&display=swap',
      'https://fonts.googleapis.com/css2?family=Rock+Salt&display=swap',
      'https://fonts.googleapis.com/css2?family=Shadows+Into+Light&display=swap',
      'https://fonts.googleapis.com/css2?family=Special+Elite&display=swap',
      'https://fonts.googleapis.com/css2?family=Walter+Turncoat&display=swap'
    ];

    fontLinks.forEach(link => {
      // تحقق من عدم وجود الرابط مسبقاً
      if (!document.querySelector(`link[href="${link}"]`)) {
        const linkElement = document.createElement('link');
        linkElement.href = link;
        linkElement.rel = 'stylesheet';
        document.head.appendChild(linkElement);
      }
    });
  }
};

// Default text style for use in the UI and SavedStylesGrid
const defaultTextStyle = {
  id: 'default-style',
  fontFamily: 'Arial, sans-serif',
  fontSize: 16,
  fontWeight: 'normal',
  color: '#000000',
  backgroundColor: 'transparent',
  textAlign: 'left',
  lineHeight: 1.5,
  letterSpacing: 0,
  textDecoration: 'none',
  textTransform: 'none',
  fontStyle: 'normal',
  opacity: 1,
  textShadow: undefined,
  WebkitTextStroke: undefined,
  transform: undefined
};

// Helper to measure text size for a given font
export function measureText(text, fontSize, fontFamily, fontWeight, fontStyle, lineHeight, maxWidth = null) {
    // Create a hidden span for measurement
    const span = document.createElement('span');
    span.style.position = 'absolute';
    span.style.visibility = 'hidden';
    span.style.whiteSpace = 'pre-wrap';
    span.style.fontSize = fontSize + 'px';
    span.style.fontFamily = fontFamily;
    span.style.fontWeight = fontWeight;
    span.style.fontStyle = fontStyle;
    span.style.lineHeight = lineHeight;
    span.style.padding = '4px';
    span.style.letterSpacing = 'normal';
    span.style.textTransform = 'none';
    span.style.textDecoration = 'none';
    span.style.width = maxWidth ? maxWidth + 'px' : 'auto';
    span.innerText = text || 'Text';
    document.body.appendChild(span);
    const rect = span.getBoundingClientRect();
    document.body.removeChild(span);
    return { width: rect.width, height: rect.height };
}

// دالة مساعدة لتحديث أبعاد العنصر النصي مع الهامش
const updateTextElementDimensions = (el, newProps = {}) => {
  const paddingW = 12;
  const paddingH = 8;
  const value = newProps.value !== undefined ? newProps.value : el.value;
  const fontSize = newProps.fontSize !== undefined ? newProps.fontSize : el.fontSize || 16;
  const fontFamily = newProps.fontFamily !== undefined ? newProps.fontFamily : el.fontFamily || 'Arial, sans-serif';
  const fontWeight = newProps.fontWeight !== undefined ? newProps.fontWeight : el.fontWeight || 'normal';
  const fontStyle = newProps.fontStyle !== undefined ? newProps.fontStyle : el.fontStyle || 'normal';
  const lineHeight = newProps.lineHeight !== undefined ? newProps.lineHeight : el.lineHeight || 1.2;
  const { width, height } = measureText(
    value,
    fontSize,
    fontFamily,
    fontWeight,
    fontStyle,
    lineHeight
  );
  return {
    width: Math.max(width + paddingW, 40),
    height: Math.max(height + paddingH, 24)
  };
};

function TextSettings() {
  // تعريف جميع متغيرات الحالة أولاً
  const { isDarkMode } = useDarkMode();
  const [aiTextContent, setAiTextContent] = useState('');
  const [aiFontSize, setAiFontSize] = useState(16);
  const [aiFontFamily, setAiFontFamily] = useState('Arial, sans-serif');
  const [aiFontWeight, setAiFontWeight] = useState('normal');
  const [aiTextColor, setAiTextColor] = useState('#000000');
  const [aiBackgroundColor, setAiBackgroundColor] = useState('transparent');
  const [aiTextAlign, setAiTextAlign] = useState('left');
  const [aiLineHeight, setAiLineHeight] = useState(1.5);
  const [aiLetterSpacing, setAiLetterSpacing] = useState(0);
  const [aiTextDecoration, setAiTextDecoration] = useState('none');
  const [aiTextTransform, setAiTextTransform] = useState('none');
  const [aiTextEffect, setAiTextEffect] = useState('none');
  const [aiTextStyle, setAiTextStyle] = useState(null);
  const [aiTextOpacity, setAiTextOpacity] = useState(100);
  const [aiTextRotation, setAiTextRotation] = useState(0);
  const [aiTextShadowColor, setAiTextShadowColor] = useState('#000000');
  const [aiTextShadowBlur, setAiTextShadowBlur] = useState(2);
  const [aiTextShadowOffset, setAiTextShadowOffset] = useState(2);
  const [isUserEditing, setIsUserEditing] = useState(false);

  const {
    savedTextStyles,
    setActiveTextStyle,
    activeTextStyleId,
    deleteTextStyle,
    getActiveTextStyle,
    addElement,
    setSelectedIds,
    selectedElement,
    updateElement,
    setSelectedElement
  } = useDesignSpace();

  // دالة لاكتشاف النص الديناميكي (من Add Field)
  const isDynamicText = selectedElement && (selectedElement.isDynamicField || (typeof selectedElement.value === 'string' && selectedElement.value.includes('{{')));

  // عند تغيير العنصر المحدد، فقط حدث aiTextContent ليعكس القيمة الحالية للنص (مرة واحدة فقط)
  useEffect(() => {
    if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
      // تعيين أن المستخدم يقوم بالتعديل لمنع التطبيق التلقائي
      setIsUserEditing(true);
      
      setAiTextContent(selectedElement.value || '');
      setAiFontFamily(selectedElement.fontFamily || 'Arial, sans-serif');
      setAiFontSize(selectedElement.fontSize || 16);
      setAiFontWeight(selectedElement.fontWeight || 'normal');
      setAiTextColor(selectedElement.color || '#000000');
      setAiBackgroundColor(selectedElement.backgroundColor || 'transparent');
      setAiTextAlign(selectedElement.textAlign || 'left');
      setAiLineHeight(selectedElement.lineHeight || 1.5);
      setAiLetterSpacing(selectedElement.letterSpacing || 0);
      setAiTextDecoration(selectedElement.textDecoration || 'none');
      setAiTextTransform(selectedElement.textTransform || 'none');
      setAiTextStyle(selectedElement.fontStyle || null);
      setAiTextOpacity(
        typeof selectedElement.opacity === 'number'
          ? Math.round(selectedElement.opacity * 100)
          : 100
      );
      setAiTextRotation(selectedElement.rotation || 0);
      setAiTextEffect(selectedElement.textEffect || 'none');
      setAiTextShadowColor(selectedElement.textShadowColor || '#000000');
      setAiTextShadowBlur(selectedElement.textShadowBlur || 2);
      setAiTextShadowOffset(selectedElement.textShadowOffset || 2);
      
      // تأكد من أن النص الديناميكي يحتفظ بخصائصه
      if (selectedElement.isDynamicField) {
        console.log('Dynamic text element selected:', selectedElement);
      }
      
      // إذا كان النص مقفل، لا تحدث حالة النمط النشط
      if (selectedElement.isStyleLocked) {
        console.log('Text style is locked, not updating active style');
        // إلغاء تفعيل النمط النشط إذا كان النص مقفل
        setActiveTextStyle(null);
      }
      
      // إعادة تعيين حالة التعديل بعد فترة قصيرة
      setTimeout(() => setIsUserEditing(false), 100);
    }
    // eslint-disable-next-line
  }, [selectedElement && selectedElement.id, setActiveTextStyle]);

  // تحميل الخطوط من Google Fonts عند تحميل المكون
  useEffect(() => {
    loadGoogleFonts();
  }, []);

  // دالة تطبيق التأثيرات على النص المحدد في منطقة التصميم
  const applyEffectsToSelectedText = useCallback(() => {
    if (!selectedElement || (selectedElement.type !== 'text' && selectedElement.type !== 'label')) {
      console.log('No text element selected');
      return;
    }

    // تحقق من حالة القفل
    if (selectedElement.isStyleLocked) {
      console.log('Text style is locked. Please unlock to edit.');
      return;
    }

    // تعيين أن المستخدم يقوم بالتعديل
    setIsUserEditing(true);

    // إنشاء كائن الأنماط المحدثة
    const updatedStyles = {
      fontFamily: aiFontFamily,
      fontSize: aiFontSize,
      fontWeight: aiFontWeight,
      color: aiTextColor,
      backgroundColor: aiBackgroundColor !== 'transparent' ? aiBackgroundColor : undefined,
      textAlign: aiTextAlign,
      lineHeight: aiLineHeight,
      letterSpacing: aiLetterSpacing,
      textDecoration: aiTextDecoration,
      textTransform: aiTextTransform,
      fontStyle: aiTextStyle,
      opacity: aiTextOpacity / 100,
      textShadow: aiTextEffect === 'shadow' ? `${aiTextShadowOffset}px ${aiTextShadowOffset}px ${aiTextShadowBlur}px ${aiTextShadowColor}` : undefined,
      WebkitTextStroke: aiTextEffect === 'outline' ? '1px #000000' : undefined,
      transform: aiTextEffect === '3d' ? 'perspective(500px) rotateX(10deg) rotateY(5deg)' : aiTextRotation !== 0 ? `rotate(${aiTextRotation}deg)` : undefined,
      // إزالة القفل عند التعديل
      isStyleLocked: false,
      lockedStyleId: null
    };

    // تطبيق الأنماط على العنصر المحدد
    updateElement(selectedElement.id, updatedStyles);
    
    // إلغاء تفعيل النمط النشط عند التعديل
    setActiveTextStyle(null);
    
    // إعادة تعيين حالة التعديل بعد فترة قصيرة
    setTimeout(() => setIsUserEditing(false), 100);
    
    console.log('Applied effects to selected text element:', selectedElement.id);
  }, [
    selectedElement, aiFontFamily, aiFontSize, aiFontWeight, aiTextColor, aiBackgroundColor,
    aiTextAlign, aiLineHeight, aiLetterSpacing, aiTextDecoration, aiTextTransform,
    aiTextStyle, aiTextOpacity, aiTextRotation, aiTextEffect, aiTextShadowColor,
    aiTextShadowBlur, aiTextShadowOffset, updateElement, setActiveTextStyle
  ]);

  // دالة تطبيق التأثيرات تلقائياً عند تغيير أي إعداد
  const applyEffectsAutomatically = useCallback(() => {
    if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label') && !selectedElement.isStyleLocked) {
      applyEffectsToSelectedText();
    }
  }, [selectedElement, applyEffectsToSelectedText]);

  // استخدام useEffect لتطبيق التأثيرات تلقائياً عند تغيير أي إعداد
  // استثناء النصوص الديناميكية والمقفلة من التطبيق التلقائي
  useEffect(() => {
    // لا تطبق التأثيرات تلقائياً على النصوص الديناميكية أو المقفلة أو عند تعديل المستخدم
    if (selectedElement && !selectedElement.isDynamicField && !selectedElement.isStyleLocked && !isUserEditing) {
      applyEffectsAutomatically();
    }
  }, [
    aiFontFamily, aiFontSize, aiFontWeight, aiTextColor, aiBackgroundColor,
    aiTextAlign, aiLineHeight, aiLetterSpacing, aiTextDecoration, aiTextTransform,
    aiTextStyle, aiTextOpacity, aiTextRotation, aiTextEffect, aiTextShadowColor,
    aiTextShadowBlur, aiTextShadowOffset, applyEffectsAutomatically, selectedElement, isUserEditing
  ]);

  // دالة إنشاء نص جديد باستخدام إعدادات Text Assistant
  const createNewTextWithAIStyles = () => {
    // إذا كان هناك تصميم نشط، استخدمه فقط لتطبيق التصميم وليس القيمة النصية
    const activeStyle = getActiveTextStyle && getActiveTextStyle();
    let textStyle = null;
    let customProps = {};
    // دائماً أضف النص 'Your Text' عند إضافة عنصر جديد
    const textToAdd = 'Your Text';
    if (activeStyle) {
      // حماية إضافية: انسخ خصائص التصميم فقط (deep copy) ولا تنسخ أي id أو value أو مرجع من عنصر قديم
      const pureStyle = JSON.parse(JSON.stringify(activeStyle));
      delete pureStyle.value;
      delete pureStyle.id;
      textStyle = { ...pureStyle };
      customProps = { ...pureStyle };
    } else {
      // استخدم النمط الافتراضي مباشرة إذا لم يوجد style نشط
      textStyle = { ...defaultTextStyle };
      customProps = { ...defaultTextStyle };
    }
    // إزالة id من customProps لو وجد
    if (customProps.id) delete customProps.id;
    try {
      // قياس أبعاد النص الجديد بناءً على style النهائي
      const paddingW = 12; // هامش عرض إضافي
      const paddingH = 8;  // هامش ارتفاع إضافي
      const { width: measuredWidth, height: measuredHeight } = measureText(
        textToAdd,
        textStyle.fontSize || 16,
        textStyle.fontFamily || 'Arial, sans-serif',
        textStyle.fontWeight || 'normal',
        textStyle.fontStyle || 'normal',
        textStyle.lineHeight || 1.2
      );
      const minWidth = 40;
      const minHeight = 24;
      const newElementId = addElement('text', textToAdd, {
        style: textStyle,
        width: Math.max(measuredWidth + paddingW, minWidth),
        height: Math.max(measuredHeight + paddingH, minHeight),
        isStyleLocked: activeStyle ? true : false,
        lockedStyleId: activeStyle ? activeStyle.id : null,
        ...customProps
      });
      if (newElementId) {
        setSelectedIds([newElementId]);
      }
    } catch (error) {
      console.error('Error adding text to canvas:', error);
    }
  };

  const textSettingHandler = (e, key, value) => {
    e.stopPropagation()
    if (!selectedElement) return;

    // تحقق من حالة القفل
    if (selectedElement.isStyleLocked) {
      console.log('Text style is locked. Please unlock to edit.');
      return;
    }

    // تعيين أن المستخدم يقوم بالتعديل
    setIsUserEditing(true);

    // تحويل الخصائص القديمة إلى الخصائص القياسية
    let actualKey = key;
    let actualValue = value;
    
    if (key === 'isBold') {
      actualKey = 'fontWeight';
      actualValue = value ? 'bold' : 'normal';
    } else if (key === 'isItalic') {
      actualKey = 'fontStyle';
      actualValue = value ? 'italic' : 'normal';
    } else if (key === 'isUnderlined') {
      actualKey = 'textDecoration';
      actualValue = value ? 'underline' : 'none';
    }
   
    setSelectedElement(prev => ({ ...prev, [actualKey]: actualValue }))
    
    
    const updateData = { 
      [actualKey]: actualValue,
      // إزالة القفل عند التعديل
      isStyleLocked: false,
      lockedStyleId: null
    };
    
    if (selectedElement.originalFieldKey) {
      updateData.originalFieldKey = selectedElement.originalFieldKey;
      updateData.isDynamicField = true;
    }
    
    updateElement(selectedElement.id, updateData);
    
    setSelectedElement(prev => ({ ...prev, ...updateData }));
    
    // إلغاء تفعيل النمط النشط عند التعديل
    setActiveTextStyle(null);
    
    // إعادة تعيين حالة التعديل بعد فترة قصيرة
    setTimeout(() => setIsUserEditing(false), 100);
  };

  // دالة لإلغاء قفل النمط
  const unlockTextStyle = () => {
    if (!selectedElement) return;
    
    // تعيين أن المستخدم يقوم بالتعديل
    setIsUserEditing(true);
    
    const updateData = { 
      isStyleLocked: false,
      lockedStyleId: null 
    };
    
    updateElement(selectedElement.id, updateData);
    setSelectedElement(prev => ({ ...prev, ...updateData }));
    
    // إلغاء تفعيل النمط النشط لتجنب إعادة تطبيقه تلقائياً
    setActiveTextStyle(null);
    
    // إعادة تعيين حالة التعديل بعد فترة قصيرة
    setTimeout(() => setIsUserEditing(false), 100);
  };

 
  const fontFamilyItemTemplate = (option) => {
    return (
      <div className="flex justify-between items-center w-full">
        <span>{option.label}</span>
        <span 
          style={{ 
            fontFamily: option.value,
            fontSize: '14px',
            color: '#666'
          }}
        >
          {option.example}
        </span>
      </div>
    );
  };

  // دوال AI Text Assistant
  const resetAiTextStyling = () => {
    setAiTextContent('Welcome to our design platform');
    setAiFontSize(16);
    setAiFontFamily('Arial, sans-serif');
    setAiFontWeight('normal');
    setAiTextColor('#000000');
    setAiBackgroundColor('transparent');
    setAiTextAlign('left');
    setAiLineHeight(1.5);
    setAiLetterSpacing(0);
    setAiTextDecoration('none');
    setAiTextTransform('none');
    setAiTextEffect('none');
    setAiTextStyle(null);
    setAiTextOpacity(100);
    setAiTextRotation(0);
    setAiTextShadowColor('#000000');
    setAiTextShadowBlur(2);
    setAiTextShadowOffset(2);
  };

  const applyAiTextStylePreset = (preset) => {
    switch(preset) {
        case 'heading1':
            setAiFontSize(32);
            setAiFontWeight('bold');
            setAiTextColor('#1f2937');
            setAiTextAlign('center');
            setAiLineHeight(1.2);
            setAiLetterSpacing(1);
            break;
        case 'heading2':
            setAiFontSize(24);
            setAiFontWeight('600');
            setAiTextColor('#374151');
            setAiTextAlign('left');
            setAiLineHeight(1.3);
            break;
        case 'subtitle':
            setAiFontSize(18);
            setAiFontWeight('500');
            setAiTextColor('#6b7280');
            setAiTextAlign('center');
            setAiTextStyle('italic');
            break;
        case 'paragraph':
            setAiFontSize(16);
            setAiFontWeight('normal');
            setAiTextColor('#374151');
            setAiTextAlign('left');
            setAiLineHeight(1.6);
            setAiLetterSpacing(0.5);
            break;
        case 'quote':
            setAiFontSize(18);
            setAiTextStyle('italic');
            setAiTextColor('#4b5563');
            setAiTextAlign('center');
            setAiBackgroundColor('#f3f4f6');
            setAiTextDecoration('none');
            break;
        case 'callout':
            setAiFontSize(16);
            setAiFontWeight('600');
            setAiTextColor('#ffffff');
            setAiBackgroundColor('#3b82f6');
            setAiTextAlign('center');
            setAiTextDecoration('none');
            break;
        case 'elegant':
            setAiFontFamily('Georgia, serif');
            setAiFontSize(20);
            setAiFontWeight('normal');
            setAiTextColor('#1f2937');
            setAiTextAlign('center');
            setAiLetterSpacing(2);
            setAiTextEffect('shadow');
            setAiTextShadowColor('#9ca3af');
            break;
        case 'modern':
            setAiFontFamily('Helvetica, sans-serif');
            setAiFontSize(18);
            setAiFontWeight('300');
            setAiTextColor('#111827');
            setAiTextAlign('left');
            setAiLetterSpacing(1);
            break;
        case '3d':
            setAiFontSize(24);
            setAiFontWeight('bold');
            setAiTextColor('#3b82f6');
            setAiTextEffect('3d');
            setAiTextAlign('center');
            break;
        case 'playful':
            setAiFontFamily('Comic Sans MS, cursive');
            setAiFontSize(20);
            setAiFontWeight('bold');
            setAiTextColor('#ec4899');
            setAiTextAlign('center');
            setAiTextEffect('shadow');
            setAiTextShadowColor('#fbbf24');
            break;
        default:
            break;
    }
  };

  // متغيرات preview لخصائص النص
  const isSelectedText = selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label');
  const previewFontFamily = isSelectedText ? (selectedElement.fontFamily || 'Arial, sans-serif') : aiFontFamily;
  const previewFontSize = isSelectedText ? (selectedElement.fontSize || 16) : aiFontSize;
  const previewFontWeight = isSelectedText ? (selectedElement.fontWeight || 'normal') : aiFontWeight;
  const previewTextColor = isSelectedText ? (selectedElement.color || '#000000') : aiTextColor;
  const previewBackgroundColor = isSelectedText ? (selectedElement.backgroundColor || 'transparent') : aiBackgroundColor;
  const previewTextAlign = isSelectedText ? (selectedElement.textAlign || 'left') : aiTextAlign;
  const previewLineHeight = isSelectedText ? (selectedElement.lineHeight || 1.5) : aiLineHeight;
  const previewLetterSpacing = isSelectedText ? (selectedElement.letterSpacing || 0) : aiLetterSpacing;
  const previewTextDecoration = isSelectedText ? (selectedElement.textDecoration || 'none') : aiTextDecoration;
  const previewTextTransform = isSelectedText ? (selectedElement.textTransform || 'none') : aiTextTransform;
  const previewFontStyle = isSelectedText ? (selectedElement.fontStyle || null) : aiTextStyle;
  const previewOpacity = isSelectedText
    ? (typeof selectedElement.opacity === 'number' ? selectedElement.opacity : 1)
    : aiTextOpacity / 100;
  const previewTextEffect = isSelectedText ? (selectedElement.textEffect || 'none') : aiTextEffect;
  const previewTextShadowColor = isSelectedText ? (selectedElement.textShadowColor || '#000000') : aiTextShadowColor;
  const previewTextShadowBlur = isSelectedText ? (selectedElement.textShadowBlur || 2) : aiTextShadowBlur;
  const previewTextShadowOffset = isSelectedText ? (selectedElement.textShadowOffset || 2) : aiTextShadowOffset;
  const previewText = isSelectedText ? (selectedElement.value || '') : (aiTextContent || 'Welcome to our design platform');

  // Grid التصميمات المحفوظة
  const SavedStylesGrid = () => (
    <div className="mb-4">
      <div className={`font-bold mb-1 text-base ${isDarkMode ? 'text-white' : 'text-gray-700'}`}>Text Styles</div>
      <div className="flex gap-2 overflow-x-auto pb-2">
        {/* Always show default style first */}
        <div
          key={defaultTextStyle.id}
          className={`relative flex flex-col items-center justify-center min-w-[80px] max-w-[120px] px-2 py-2 rounded border cursor-pointer transition-all duration-150 ${!activeTextStyleId ? (isDarkMode ? 'border-blue-400 bg-blue-900/30 shadow' : 'border-blue-600 bg-blue-50 shadow') : (isDarkMode ? 'border-gray-600 bg-gray-800 hover:bg-gray-700' : 'border-gray-200 bg-white hover:bg-gray-50')}`}
          onClick={() => {
            setActiveTextStyle(null);
            // عند الضغط على Default طبّق النمط الافتراضي مباشرة على العنصر النصي المحدد
            if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
              updateElement(selectedElement.id, { 
                ...defaultTextStyle,
                isStyleLocked: false,
                lockedStyleId: null
              });
            }
          }}
          title="Default style"
        >
          <span
            style={{
              fontFamily: defaultTextStyle.fontFamily,
              fontSize: defaultTextStyle.fontSize,
              fontWeight: defaultTextStyle.fontWeight,
              color: defaultTextStyle.color,
              backgroundColor: defaultTextStyle.backgroundColor,
              textAlign: defaultTextStyle.textAlign,
              lineHeight: defaultTextStyle.lineHeight,
              letterSpacing: defaultTextStyle.letterSpacing,
              textDecoration: defaultTextStyle.textDecoration,
              textTransform: defaultTextStyle.textTransform,
              fontStyle: defaultTextStyle.fontStyle,
              opacity: defaultTextStyle.opacity,
              textShadow: defaultTextStyle.textShadow,
              WebkitTextStroke: defaultTextStyle.WebkitTextStroke,
              transform: defaultTextStyle.transform,
              padding: '2px 4px',
              borderRadius: 4,
              minWidth: 60,
              minHeight: 24,
              display: 'block',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              maxWidth: 100
            }}
          >
            Default
          </span>
          {!activeTextStyleId && (
            <FaRegStar size={18} className="absolute top-1 right-1 text-yellow-500" title="Active style" />
          )}
        </div>
        {/* Render saved styles after default */}
        {savedTextStyles && savedTextStyles.length > 0 ? (
          savedTextStyles.map(style => (
            <div
              key={style.id}
              className={`relative flex flex-col items-center justify-center min-w-[80px] max-w-[120px] px-2 py-2 rounded border cursor-pointer transition-all duration-150 ${activeTextStyleId === style.id ? (isDarkMode ? 'border-blue-400 bg-blue-900/30 shadow' : 'border-blue-600 bg-blue-50 shadow') : (isDarkMode ? 'border-gray-600 bg-gray-800 hover:bg-gray-700' : 'border-gray-200 bg-white hover:bg-gray-50')}`}
              onClick={() => {
                setActiveTextStyle(style.id);
                // تطبيق النمط على العنصر المحدد مع قفله
                if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
                  const styleProps = { ...style };
                  delete styleProps.id;
                  updateElement(selectedElement.id, { 
                    ...styleProps,
                    isStyleLocked: true,
                    lockedStyleId: style.id
                  });
                }
              }}
              title="Click to apply and lock style"
            >
              <span
                style={{
                  fontFamily: style.fontFamily,
                  fontSize: style.fontSize || 16,
                  fontWeight: style.fontWeight,
                  color: style.color,
                  backgroundColor: style.backgroundColor,
                  textAlign: style.textAlign,
                  lineHeight: style.lineHeight,
                  letterSpacing: style.letterSpacing,
                  textDecoration: style.textDecoration,
                  textTransform: style.textTransform,
                  fontStyle: style.fontStyle,
                  opacity: style.opacity,
                  textShadow: style.textShadow,
                  WebkitTextStroke: style.WebkitTextStroke,
                  transform: style.transform,
                  padding: '2px 4px',
                  borderRadius: 4,
                  minWidth: 60,
                  minHeight: 24,
                  display: 'block',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  maxWidth: 100
                }}
              >
                Text Style
              </span>
              {activeTextStyleId === style.id && (
                <FaRegStar size={18} className="absolute top-1 right-1 text-yellow-500" title="Active style" />
              )}
              <button
                className={`absolute bottom-1 right-1 text-gray-400 hover:text-red-500 p-0.5 ${isDarkMode ? 'bg-gray-900' : 'bg-white'} rounded-full`}
                style={{ fontSize: 12, lineHeight: 1 }}
                onClick={e => { e.stopPropagation(); deleteTextStyle(style.id); }}
                title="Delete style"
              >
                <FaTrash size={12} />
              </button>
            </div>
          ))
        ) : (
          <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-400'} italic text-xs`}>No saved styles yet</div>
        )}
      </div>
    </div>
  );

  return (
    <>
      {/* Grid التصميمات المحفوظة */}
      <SavedStylesGrid />

      <div className="flex justify-start">
        <button className="w-full me-1 my-2 add-element-btn" onClick={createNewTextWithAIStyles}>
          <IoPricetagsOutline size={24} className="mb-2" />
          <span>Add Text</span>
        </button>
      </div>

      <div className="flex flex-col justify-start">
        <h4 className={`font-bold mt-4 mb-2 ${isDarkMode ? 'text-white' : 'text-[#676666]'}`}>Add Field</h4>
        <div className="flex items-center mb-3">
          <FieldsDropdown />
        </div>
      </div>

                                                                                                <div className="flex flex-col justify-start">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <h4 className={`font-bold mt-4 mb-2 ${isDarkMode ? 'text-white' : 'text-[#676666]'}`}>Edit Text</h4>
            {selectedElement && selectedElement.isStyleLocked && (
              <span className={`ml-2 px-2 py-1 text-xs rounded-md ${
                isDarkMode 
                  ? 'bg-yellow-600 text-white' 
                  : 'bg-yellow-500 text-white'
              }`} title="Style is locked">
                🔒 Locked
              </span>
            )}
          </div>
          {selectedElement && selectedElement.isStyleLocked && (
            <button
              className={`px-3 py-1 text-xs rounded-md transition-all duration-200 ${
                isDarkMode 
                  ? 'bg-orange-600 hover:bg-orange-700 text-white' 
                  : 'bg-orange-500 hover:bg-orange-600 text-white'
              }`}
              onClick={unlockTextStyle}
              title="Unlock style to allow editing"
            >
              🔓 Unlock Style
            </button>
          )}
        </div>

        <div className="flex items-center mb-4">
          {/* alignment */}
          <div className="w-6/12 flex justify-center items-center">
            {
              alignmentsOptions?.map((option, index) => {
                return (
                  <button key={`alignment_${index}`}
                    className={`w-3/12 p-2 mx-1 ${selectedElement?.textAlign === option.value ? "active-bh" : ""}`}
                    onClick={(e) => { textSettingHandler(e, "textAlign", option?.value) }}
                    title={`Align ${option.value}`}
                  >
                    {option?.icon}
                  </button>)
              })
            }
          </div>

          <div className="w-6/12 flex justify-evenly items-center">
            <button
              className={`w-3/12 p-2 mx-1 ${selectedElement?.fontWeight === 'bold' ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "isBold", selectedElement?.fontWeight !== 'bold') }}
              title="Bold"
            >
              <FaBold className="mx-auto" />
            </button>

            {/* Italic */}
            <button
              className={`w-3/12 p-2 mx-1 ${selectedElement?.fontStyle === 'italic' ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "isItalic", selectedElement?.fontStyle !== 'italic') }}
              title="Italic"
            >
              <FaItalic className="mx-auto" />
            </button>
            {/* text decoration */}
            <button className={`w-3/12 p-2 mx-1 ${selectedElement?.textDecoration === 'underline' ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "isUnderlined", selectedElement?.textDecoration !== 'underline') }}
              title="Underline">
              <FaUnderline className="mx-auto" />
            </button>
          </div>
        </div>
        <div className="flex items-center mb-4">
          {/* colors */}
          <div className="w-6/12 flex justify-between items-center">
            {
              colorsOptions?.map((option, index) => {
                return (
                  <button key={`color_${index}`}
                    className={`w-2/12 p-3 mx-1 me-1 shadow-lg rounded-[3px] ${selectedElement?.color === option ? "border-2 border-[#00c7b1] scale-110" : ""}`}
                    style={{
                      backgroundColor: option,
                      border: option === "#ffffff" ? "1.5px solid #d1d5db" : undefined,
                      boxShadow: option === "#ffffff" ? "0 0 0 2px #e5e7eb" : undefined
                    }}
                    onClick={(e) => { textSettingHandler(e, "color", option) }}
                    title={`Color: ${option}`}
                  >
                    {selectedElement?.color === option && (
                      <span className="text-white text-xs">✓</span>
                    )}
                  </button>)
              })
            }
          </div>

          {/* letter case */}
          <div className="w-6/12 flex justify-between items-center">
            <button
              className={`w-3/12 p-1 mx-1 ${selectedElement?.textTransform === "capitalize" ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "textTransform", "capitalize") }}
              title="Capitalize"
            >
              <LuCaseSensitive className="mx-auto" size={30} />
            </button>

            <button
              className={`w-3/12 p-1 mx-1 ${selectedElement?.textTransform === "uppercase" ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "textTransform", "uppercase") }}
              title="Uppercase"
            >
              <LuCaseUpper className="mx-auto" size={30} />
            </button>

            <button className={`w-3/12 p-1 mx-1 ${selectedElement?.textTransform === "lowercase" ? "active-bh" : ""}`}
              onClick={(e) => { textSettingHandler(e, "textTransform", "lowercase") }}
              title="Lowercase">
              <LuCaseLower className="mx-auto" size={30} />
            </button>
          </div>
        </div>
      </div>

      {/* AI Text Assistant Section */}
      <div className={`flex flex-col justify-start mt-6 text-assistant ${isDarkMode ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-4 rounded-xl border border-gray-700/50 shadow-2xl' : ''}`}>
        <h4 className={`font-bold mt-4 mb-2 text-xl ${isDarkMode ? 'text-white' : 'text-[#676666]'}`} style={isDarkMode ? { color: '#ffffff !important', textShadow: 'none !important' } : {}}> 
          {isDarkMode ? '✨ Text Assistant' : 'Text Assistant'}
        </h4>
        
        {/* Instructions panel */}
        <div className={`mb-4 p-4 rounded-xl border instructions-panel ${isDarkMode ? 'bg-gradient-to-br from-gray-900/90 via-gray-800/80 to-gray-700/70 border-gray-700/60 shadow-lg backdrop-blur-sm' : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-200 shadow-lg backdrop-blur-sm'}`}>
          <h5 className={`text-sm font-medium mb-3 flex items-center ${isDarkMode ? 'text-blue-100' : 'text-gray-800'}`}>
            <span className={`text-xs px-3 py-1.5 rounded-full mr-3 font-semibold ${isDarkMode ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg' : 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'}`}>
              💡 How to use
            </span>
            <span className={isDarkMode ? 'text-blue-200' : 'text-gray-700'}>Quick Guide:</span>
          </h5>
          <ol className={`text-xs list-decimal pl-6 space-y-2 ${isDarkMode ? 'text-blue-50' : 'text-gray-700'}`}>
            <li className={isDarkMode ? 'text-blue-100' : 'text-gray-600'}>Write the text you want to style in the text box</li>
            <li className={isDarkMode ? 'text-blue-100' : 'text-gray-600'}>Choose a text style from presets or customize</li>
            <li className={isDarkMode ? 'text-blue-100' : 'text-gray-600'}>Adjust text properties (size, color, font, etc.)</li>
            <li className={isDarkMode ? 'text-blue-100' : 'text-gray-600'}>See the result instantly in the preview</li>
            <li className={isDarkMode ? 'text-blue-100' : 'text-gray-600'}>Click &quot;Add to Canvas&quot; to add the text to the design</li>
            <li className={isDarkMode ? 'text-blue-100' : 'text-gray-600'}>Or select an existing text and effects will be applied automatically</li>
          </ol>
        </div>

        {/* Text Input */}
        <div className={`mb-4 ${isDarkMode ? 'bg-gradient-to-r from-gray-800/30 to-gray-700/30 p-3 rounded-lg border border-gray-600/30' : ''}`}>
          <label htmlFor="textContent" className={`block text-sm font-medium mb-10 ${isDarkMode ? 'text-gray-100' : ''}`}>
            <span className={`px-3 py-1.5 rounded-full text-xs font-semibold mr-3 ${isDarkMode ? 'bg-gradient-to-r from-yellow-400 to-orange-400 text-gray-900 shadow-md' : 'bg-yellow-100 text-yellow-800'}`}>
              ✏️ 1
            </span>
            <span className={isDarkMode ? 'text-gray-200' : ''}>Your Text</span>
          </label>
          <InputText
            id="textContent"
            value={aiTextContent}
            onInput={(e) => {
              let value = e.target.value;
              if (value.trim() === '') {
                value = 'Your Text';
              }
              setAiTextContent(value);
              if (!selectedElement || !(selectedElement.type === 'text' || selectedElement.type === 'label')) {
                return;
              }
              const dims = updateTextElementDimensions(selectedElement, { value });
              updateElement(selectedElement.id, { value, ...dims });
            }}
            placeholder="Enter your text here"
            className={`w-full ${isDarkMode ? 'mt-5 bg-gray-800/80 border-gray-500/50 text-gray-100 placeholder-gray-400 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : 'bg-white border-gray-300 text-gray-800 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 rounded-lg shadow-sm'}`}
            disabled={isDynamicText}
          />
        </div>

        {/* Preview */}
        <div className={`mb-10 ${isDarkMode ? 'bg-gradient-to-r from-gray-800/30 to-gray-700/30 p-3 rounded-lg border border-gray-600/30' : ''}`}>
          <label className={`block text-sm font-medium mb-3 ${isDarkMode ? 'text-gray-100' : ''}`}>
            <span className={`px-3 py-1.5 rounded-full text-xs font-semibold mr-3 ${isDarkMode ? 'bg-gradient-to-r from-green-400 to-emerald-400 text-gray-900 shadow-md' : 'bg-yellow-100 text-yellow-800'}`}>
              👁️ 2
            </span>
            <span className={isDarkMode ? 'text-gray-200' : ''}>Preview</span>
          </label>
          <div
            className={` mt-5 p-4 border-2 rounded-xl shadow-lg overflow-hidden preview-area ${isDarkMode ? 'border-gray-500/50 bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm' : 'border-blue-200 bg-white'}`}
            style={{
              minHeight: '100px',
            }}
          >
            <div
              style={{
                fontFamily: previewFontFamily,
                fontSize: `${previewFontSize}px`,
                fontWeight: previewFontWeight,
                color: isDarkMode && previewTextColor === '#000000' ? '#ffffff' : previewTextColor,
                backgroundColor: previewBackgroundColor,
                textAlign: previewTextAlign,
                lineHeight: previewLineHeight,
                letterSpacing: `${previewLetterSpacing}px`,
                textDecoration: previewTextDecoration,
                textTransform: previewTextTransform,
                fontStyle: previewFontStyle,
                opacity: previewOpacity,
                textShadow: previewTextEffect === 'shadow' ? `${previewTextShadowOffset}px ${previewTextShadowOffset}px ${previewTextShadowBlur}px ${previewTextShadowColor}` : 'none',
                WebkitTextStroke: previewTextEffect === 'outline' ? (isDarkMode ? '1px #ffffff' : '1px #000000') : 'none',
                padding: previewBackgroundColor !== 'transparent' ? '10px' : '0',
                transform: previewTextEffect === '3d' ? 'perspective(500px) rotateX(10deg) rotateY(5deg)' : 'none',
              }}
            >
              {previewText}
            </div>
          </div>
          {/* Action Buttons */}
          <div className="flex justify-end mt-3">
            <Button
              label="Add to Canvas"
              icon="pi pi-plus"
              className={`p-button-sm p-button-raised p-button-primary ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                fontWeight: 'bold',
                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',
                borderRadius: '8px'
              } : {
                backgroundColor: '#4338ca',
                borderColor: '#4338ca',
                fontWeight: 'bold',
                boxShadow: '0 4px 6px -1px rgba(67, 56, 202, 0.3)'
              }}
              onClick={createNewTextWithAIStyles}
            />
          </div>
        </div>

        {/* Style Presets */}
        <div className={`mb-4 ${isDarkMode ? 'bg-gradient-to-r from-gray-800/30 to-gray-700/30 p-3 rounded-lg border border-gray-600/30' : ''}`}>
          <label className={`block text-sm font-medium mb-10 ${isDarkMode ? 'text-gray-100' : ''}`}>
            <span className={`px-3 mb-10 py-1.5 rounded-full text-xs font-semibold mr-3 ${isDarkMode ? 'bg-gradient-to-r from-purple-400 to-pink-400 text-gray-900 shadow-md' : 'bg-yellow-100 text-yellow-800'}`}>
              🎨 3
            </span>
            <span className={isDarkMode ? 'text-gray-200' : ''}>Style Presets</span>
          </label>
          <div className="grid grid-cols-2 gap-2 mt-5">
            <Button 
              label="Heading 1" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('heading1')} 
            />
            <Button 
              label="Heading 2" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('heading2')} 
            />
            <Button 
              label="Subtitle" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('subtitle')} 
            />
            <Button 
              label="Paragraph" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('paragraph')} 
            />
            <Button 
              label="Quote" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('quote')} 
            />
            <Button 
              label="Callout" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('callout')} 
            />
            <Button 
              label="Elegant" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('elegant')} 
            />
            <Button 
              label="Modern" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('modern')} 
            />
            <Button 
              label="3D Text" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('3d')} 
            />
            <Button 
              label="Playful" 
              className={`p-button-sm p-button-outlined ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                color: '#a78bfa',
                borderRadius: '8px'
              } : {}}
              onClick={() => applyAiTextStylePreset('playful')} 
            />
          </div>
        </div>

        {/* Custom Styling */}
        <div className={`mb-4 custom-styling-container ${isDarkMode ? 'bg-gradient-to-r from-gray-800/80 to-gray-700/80 p-3 rounded-lg border border-gray-600/50' : 'bg-gradient-to-r from-blue-50/80 to-indigo-50/80 p-3 rounded-lg border border-blue-200/50 shadow-sm'}`}>
          <div className="flex justify-between items-center mb-3">
            <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-100' : ''}`}>
              <span className={`px-3 py-1.5 rounded-full text-xs font-semibold mr-3 ${isDarkMode ? 'bg-gradient-to-r from-indigo-400 to-blue-400 text-gray-900 shadow-md' : 'bg-yellow-100 text-yellow-800'}`}>
                ⚙️ 4
              </span>
              <span className={isDarkMode ? 'text-gray-200' : ''}>Custom Styling</span>
            </label>
            <Button
              label="Reset"
              icon="pi pi-refresh"
              className={`p-button-outlined p-button-sm ${isDarkMode ? 'hover:shadow-lg transition-all duration-300' : ''}`}
              style={isDarkMode ? {
                background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%)',
                border: '1px solid rgba(239, 68, 68, 0.3)',
                color: '#f87171',
                borderRadius: '8px'
              } : {}}
              onClick={resetAiTextStyling}
            />
          </div>
          <div className="grid grid-cols-2 gap-3">
            {/* Font Family */}
            <div className="col-span-2">
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Font Family</label>
              <Dropdown
                value={aiFontFamily}
                options={aiFontFamilyOptions}
                onChange={(e) => {
                  setAiFontFamily(e.value);
                  if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
                    const dims = updateTextElementDimensions(selectedElement, { fontFamily: e.value });
                    updateElement(selectedElement.id, {
                      fontFamily: e.value,
                      ...dims
                    });
                  }
                }}
                itemTemplate={fontFamilyItemTemplate}
                className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
                filter
                filterPlaceholder="Search fonts..."
                showClear
                virtualScrollerOptions={{ itemSize: 50 }}
                maxVisibleOptions={10}
                panelClassName={`font-dropdown-panel ${isDarkMode ? 'bg-gray-800/95 border-gray-600/50 backdrop-blur-sm shadow-xl' : ''}`}
              />
            </div>
            {/* Font Size */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Font Size: {aiFontSize}px</label>
              <Slider
                value={aiFontSize}
                onChange={(e) => {
                  setAiFontSize(e.value);
                  if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
                    const dims = updateTextElementDimensions(selectedElement, { fontSize: e.value });
                    updateElement(selectedElement.id, {
                      fontSize: e.value,
                      ...dims
                    });
                  }
                }}
                min={8}
                max={72}
                className={isDarkMode ? 'text-white' : ''}
              />
            </div>
            {/* Font Weight */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Font Weight</label>
              <Dropdown
                value={aiFontWeight}
                options={aiFontWeightOptions}
                onChange={(e) => {
                  setAiFontWeight(e.value);
                  if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
                    const dims = updateTextElementDimensions(selectedElement, { fontWeight: e.value });
                    updateElement(selectedElement.id, {
                      fontWeight: e.value,
                      ...dims
                    });
                  }
                }}
                className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
              />
            </div>
            {/* Font Style */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Font Style</label>
              <Dropdown
                value={aiTextStyle}
                options={[
                  { label: 'Normal', value: 'normal' },
                  { label: 'Italic', value: 'italic' },
                  { label: 'Oblique', value: 'oblique' }
                ]}
                onChange={(e) => {
                  setAiTextStyle(e.value);
                  if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
                    const dims = updateTextElementDimensions(selectedElement, { fontStyle: e.value });
                    updateElement(selectedElement.id, {
                      fontStyle: e.value,
                      ...dims
                    });
                  }
                }}
                className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
              />
            </div>
            {/* Text Color */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Text Color</label>
              <div className="flex items-center">
                <input
                  type="color"
                  value={aiTextColor}
                  onChange={(e) => setAiTextColor(e.target.value)}
                  className={`w-8 h-8 mr-2 border rounded cursor-pointer ${isDarkMode ? 'bg-gray-800 border-gray-600' : ''}`}
                />
                <InputText
                  value={aiTextColor}
                  onChange={(e) => setAiTextColor(e.target.value)}
                  className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
                />
              </div>
            </div>
            {/* Background Color */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Background Color</label>
              <div className="flex items-center">
                <input
                  type="color"
                  value={aiBackgroundColor === 'transparent' ? '#ffffff' : aiBackgroundColor}
                  onChange={(e) => setAiBackgroundColor(e.target.value)}
                  className={`w-8 h-8 mr-2 border rounded cursor-pointer ${isDarkMode ? 'bg-gray-800 border-gray-600' : ''}`}
                />
                <InputText
                  value={aiBackgroundColor}
                  onChange={(e) => setAiBackgroundColor(e.target.value)}
                  className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
                />
              </div>
            </div>
            {/* Text Alignment */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Text Alignment</label>
              <Dropdown
                value={aiTextAlign}
                options={aiTextAlignOptions}
                onChange={(e) => setAiTextAlign(e.value)}
                className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
              />
            </div>
            {/* Line Height */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Line Height: {aiLineHeight}</label>
              <Slider
                value={aiLineHeight}
                onChange={(e) => {
                  setAiLineHeight(e.value);
                  if (selectedElement && (selectedElement.type === 'text' || selectedElement.type === 'label')) {
                    const dims = updateTextElementDimensions(selectedElement, { lineHeight: e.value });
                    updateElement(selectedElement.id, {
                      lineHeight: e.value,
                      ...dims
                    });
                  }
                }}
                min={0.8}
                max={3}
                step={0.1}
                className={isDarkMode ? 'text-white' : ''}
              />
            </div>
            {/* Letter Spacing */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Letter Spacing: {aiLetterSpacing}px</label>
              <Slider
                value={aiLetterSpacing}
                onChange={(e) => setAiLetterSpacing(e.value)}
                min={-2}
                max={10}
                step={0.1}
                className={isDarkMode ? 'text-white' : ''}
              />
            </div>
            {/* Text Decoration */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Text Decoration</label>
              <Dropdown
                value={aiTextDecoration}
                options={aiTextDecorationOptions}
                onChange={(e) => setAiTextDecoration(e.value)}
                className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
              />
            </div>
            {/* Text Transform */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Text Transform</label>
              <Dropdown
                value={aiTextTransform}
                options={aiTextTransformOptions}
                onChange={(e) => setAiTextTransform(e.value)}
                className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
              />
            </div>
            {/* Text Effect */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Text Effect</label>
              <Dropdown
                value={aiTextEffect}
                options={aiTextEffectOptions}
                onChange={(e) => setAiTextEffect(e.value)}
                className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
              />
            </div>
            {/* Opacity */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Opacity: {aiTextOpacity}%</label>
              <Slider
                value={aiTextOpacity}
                onChange={(e) => setAiTextOpacity(e.value)}
                min={0}
                max={100}
                className={isDarkMode ? 'text-white' : ''}
              />
            </div>
            {/* Rotation */}
            <div>
              <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Rotation: {aiTextRotation}°</label>
              <Slider
                value={aiTextRotation}
                onChange={(e) => setAiTextRotation(e.value)}
                min={-180}
                max={180}
                className={isDarkMode ? 'text-white' : ''}
              />
            </div>
            {/* Shadow Controls - only show if effect is shadow */}
            {aiTextEffect === 'shadow' && (
              <>
                <div>
                  <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Shadow Color</label>
                  <div className="flex items-center">
                    <input
                      type="color"
                      value={aiTextShadowColor}
                      onChange={(e) => setAiTextShadowColor(e.target.value)}
                      className={`w-8 h-8 mr-2 border rounded cursor-pointer ${isDarkMode ? 'bg-gray-800 border-gray-600' : ''}`}
                    />
                    <InputText
                      value={aiTextShadowColor}
                      onChange={(e) => setAiTextShadowColor(e.target.value)}
                      className={`w-full ${isDarkMode ? 'bg-gray-800/80 border-gray-600/70 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-lg' : ''}`}
                    />
                  </div>
                </div>

                <div>
                  <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Shadow Blur: {aiTextShadowBlur}px</label>
                  <Slider
                    value={aiTextShadowBlur}
                    onChange={(e) => setAiTextShadowBlur(e.value)}
                    min={0}
                    max={20}
                    className={isDarkMode ? 'text-white' : ''}
                  />
                </div>

                <div>
                  <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-white' : ''}`}>Shadow Offset: {aiTextShadowOffset}px</label>
                  <Slider
                    value={aiTextShadowOffset}
                    onChange={(e) => setAiTextShadowOffset(e.value)}
                    min={0}
                    max={10}
                    className={isDarkMode ? 'text-white' : ''}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default TextSettings

// إضافة CSS مخصص لـ Text Assistant
const textAssistantStyles = `
.text-assistant {
  pointer-events: auto !important;
}

.text-assistant * {
  pointer-events: auto !important;
}

.text-assistant .p-button {
  pointer-events: auto !important;
}

.text-assistant .p-dropdown {
  pointer-events: auto !important;
}

.text-assistant .p-slider {
  pointer-events: auto !important;
}

.text-assistant input[type="color"] {
  pointer-events: auto !important;
}

.text-assistant .p-inputtext {
  pointer-events: auto !important;
}

/* تحسين مظهر الأزرار */
.text-assistant .p-button.p-button-primary {
  background-color: #4338ca !important;
  border-color: #4338ca !important;
  color: white !important;
}

.text-assistant .p-button.p-button-primary:hover {
  background-color: #3730a3 !important;
  border-color: #3730a3 !important;
}

.text-assistant .p-button.p-button-outlined {
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined:hover {
  background-color: rgba(67, 56, 202, 0.1) !important;
}

/* تحسين مظهر الأزرار المعطلة */
.text-assistant .p-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* تحسين مظهر الألوان */
.text-assistant input[type="color"] {
  border: 2px solid #e5e7eb !important;
  border-radius: 4px !important;
  cursor: pointer !important;
}

.text-assistant input[type="color"]:hover {
  border-color: #d1d5db !important;
}

/* تحسين مظهر القوائم المنسدلة */
.text-assistant .p-dropdown {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.text-assistant .p-dropdown:hover {
  border-color: #9ca3af !important;
}

/* تحسين مظهر حقول الإدخال */
.text-assistant .p-inputtext {
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  color: #374151 !important;
}

.text-assistant .p-inputtext:focus {
  border-color: #4338ca !important;
  box-shadow: 0 0 0 3px rgba(67, 56, 202, 0.1) !important;
  background-color: white !important;
}

/* تحسين مظهر الشرائح */
.text-assistant .p-slider {
  margin: 0.5rem 0 !important;
}

.text-assistant .p-slider .p-slider-handle {
  background-color: #4338ca !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.text-assistant .p-slider .p-slider-range {
  background-color: #4338ca !important;
}

/* تحسين مظهر منطقة المعاينة */
.text-assistant .preview-area {
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* تحسين مظهر التعليمات */
.text-assistant .instructions-panel {
  background: linear-gradient(to bottom right, #f0f9ff, #e0f2fe, #f0f4ff) !important;
  border: 1px solid #bfdbfe !important;
  border-radius: 8px !important;
}

/* تحسين مظهر العناوين */
.text-assistant h4 {
  color: #374151 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
}

/* تحسين مظهر التسميات */
.text-assistant label {
  color: #4b5563 !important;
  font-weight: 500 !important;
  margin-bottom: 0.25rem !important;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* ضمان عمل الخطوط العربية */
@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط الإنجليزية */
@font-face {
  font-family: 'Roboto';
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Playfair Display';
  src: url('https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qC0s.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Pacifico';
  src: url('https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6MmBp0u-.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bebas Neue';
  src: url('https://fonts.gstatic.com/s/bebasneue/v9/JTUSjIg69CK48gW7PXoo9WlhI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dancing Script';
  src: url('https://fonts.gstatic.com/s/dancingscript/v24/If2cXTr6YS-zF4S-kcSWSVi_sxjsohD9F50Ruu7BMSo3ROp6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Orbitron';
  src: url('https://fonts.gstatic.com/s/orbitron/v25/yMJMMIlzdpvBhQQL_SC3X9yhF25-T1nyGy6BoWgz.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lobster';
  src: url('https://fonts.gstatic.com/s/lobster/v28/neILzCirqoswsqX9zoKmNg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abril Fatface';
  src: url('https://fonts.gstatic.com/s/abrilfatface/v12/zOL64pLDpnLkHmFHMKNTQ4g5bs3t6GqJKxeA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Righteous';
  src: url('https://fonts.gstatic.com/s/righteous/v9/1cXxaUPXBpj2rGoU7C9WiHGA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Permanent Marker';
  src: url('https://fonts.gstatic.com/s/permanentmarker/v10/Fh4uPib6I9yqygr9j2ePTWi4QKqyC6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Fredoka One';
  src: url('https://fonts.gstatic.com/s/fredokaone/v8/k3kUo8kEI-tA1RRcTZGmTlHGCaI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bangers';
  src: url('https://fonts.gstatic.com/s/bangers/v12/FeVQS0BTqb0h60ACH55Q2J5hm.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Chewy';
  src: url('https://fonts.gstatic.com/s/chewy/v12/uK_94ruUb-k-wn52KjI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kaushan Script';
  src: url('https://fonts.gstatic.com/s/kaushanscript/v14/vm8vdRfvXFLG3OLnsO15WYS5DG74wNc.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Satisfy';
  src: url('https://fonts.gstatic.com/s/satisfy/v12/rP2Hp2yn6lkG50LoCZOIHQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Great Vibes';
  src: url('https://fonts.gstatic.com/s/greatvibes/v12/RWmMoLWRv4ITMsfS8c0tPvQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cinzel';
  src: url('https://fonts.gstatic.com/s/cinzel/v16/8vIU7ww63mVu7gtR-kwKxNvkNOjw-tbnTYrvTO5c4A.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'UnifrakturMaguntia';
  src: url('https://fonts.gstatic.com/s/unifrakturmaguntia/v12/WWXPlieVYwiGNomYU-ciRLRvEmK7oaVem2ZI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Creepster';
  src: url('https://fonts.gstatic.com/s/creepster/v12/AlZy_zVUqJz4yMrniH4Rcn35.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Faster One';
  src: url('https://fonts.gstatic.com/s/fasterone/v12/H4ciBXCHmdfClFd-vWhxXPX5.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Press Start 2P';
  src: url('https://fonts.gstatic.com/s/pressstart2p/v14/e3t4euO8T-267oIAQAu6jDQyK3nVivM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'VT323';
  src: url('https://fonts.gstatic.com/s/vt323/v12/pxiKyp0ihIEF2isfFJU.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Share Tech Mono';
  src: url('https://fonts.gstatic.com/s/sharetechmono/v10/J7aHnp1uDWRBEqV98dVQztYldFcLowEF.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Space Mono';
  src: url('https://fonts.gstatic.com/s/spacemono/v10/i7dPIFZifjKcF5UAWdDRYEF8RQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Major Mono Display';
  src: url('https://fonts.gstatic.com/s/majormonodisplay/v6/RWmVoLyb5fEqtsfBX9PDZIGr2tFubRhLCn2QIndPww.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Crimson Text';
  src: url('https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJfbwhT.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Libre Baskerville';
  src: url('https://fonts.gstatic.com/s/librebaskerville/v14/kmKnZrc3Hgbbcjq75U4uslyuy4kn0qNZaxY.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lora';
  src: url('https://fonts.gstatic.com/s/lora/v26/0QI6MX1D_JOuGQbT0gvTJPa787weuyJGmKM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('https://fonts.gstatic.com/s/sourcesanspro/v21/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('https://fonts.gstatic.com/s/nunito/v24/XRXI3I6Li01BKofiOc5wtlZ2di8HDLshdTQ3jw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Quicksand';
  src: url('https://fonts.gstatic.com/s/quicksand/v29/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-xw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Comfortaa';
  src: url('https://fonts.gstatic.com/s/comfortaa/v37/1Pt_g8LJRfWJmhDAuUsSQamb1W0lwk4S4TbMDrMfJQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Varela Round';
  src: url('https://fonts.gstatic.com/s/varelaround/v13/w8gdH283Tvk__Lua32TysjIfp8uPLdshZg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Maven Pro';
  src: url('https://fonts.gstatic.com/s/mavenpro/v32/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8SX25nCpkp4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Exo 2';
  src: url('https://fonts.gstatic.com/s/exo2/v20/7cH1v4okm5zmbvwkAx_sfcEuiD8jWfWcPg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rajdhani';
  src: url('https://fonts.gstatic.com/s/rajdhani/v15/LDI2apCSOBg7S-QT7paQc6M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Titillium Web';
  src: url('https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmCA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Josefin Sans';
  src: url('https://fonts.gstatic.com/s/josefinsans/v25/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQbMZhKg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abel';
  src: url('https://fonts.gstatic.com/s/abel/v18/MwQ5bhbm2POE6VhLPw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anton';
  src: url('https://fonts.gstatic.com/s/anton/v23/1Ptgg87LROyAm3Kz-Co.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bungee';
  src: url('https://fonts.gstatic.com/s/bungee/v6/N0bU2SZBIuF2PU_0AnR1Gd8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Black Ops One';
  src: url('https://fonts.gstatic.com/s/blackopsone/v12/qWcsB6-ypo7xBdr6Xshe96H3aDbbtwkh.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Russo One';
  src: url('https://fonts.gstatic.com/s/russoone/v14/Z9XUDmZRWg6M1LvRYsHOz8mJ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Audiowide';
  src: url('https://fonts.gstatic.com/s/audiowide/v8/l7gdbjpo0cum0ckerWCtkQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa One';
  src: url('https://fonts.gstatic.com/s/changaone/v13/xfu00W3wXn3QLUJXhzq46AbouLfbK64.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Sniglet';
  src: url('https://fonts.gstatic.com/s/sniglet/v13/cIf9MaFLtkE2UupGgQYhiA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Boogaloo';
  src: url('https://fonts.gstatic.com/s/boogaloo/v12/kmK-Zq45GAvOdnaW6x1F_SrQo.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bubblegum Sans';
  src: url('https://fonts.gstatic.com/s/bubblegumsans/v12/AYCSpXb_Z9EORv1M5QTjEzMEtdaHzoPPbqR4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cherry Cream Soda';
  src: url('https://fonts.gstatic.com/s/cherrycreamsoda/v12/UMBIrOxBrW6w2FFyi9paG0fdVdRciQd6A4Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Freckle Face';
  src: url('https://fonts.gstatic.com/s/freckleface/v9/AMOWz4SXrmKHCvXTohxY-YI0Uw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gloria Hallelujah';
  src: url('https://fonts.gstatic.com/s/gloriahallelujah/v12/LYjYdHv3pUkNBMypJh7elzXKCM41xtdECq76mk.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Indie Flower';
  src: url('https://fonts.gstatic.com/s/indieflower/v17/m8JVjfNVeKWVnh3QMuKkFcZVaUuC.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kalam';
  src: url('https://fonts.gstatic.com/s/kalam/v11/YA9Qr0Wd4kDdMtD6GgLLmCUItqGt.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Patrick Hand';
  src: url('https://fonts.gstatic.com/s/patrickhand/v14/Ln1FzOA-y6TkwHrOUc6NnUjKRp9m.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Reenie Beanie';
  src: url('https://fonts.gstatic.com/s/reeniebeanie/v11/z7NSdR76eDkaJKZJFkkjuvWxXPq1qw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rock Salt';
  src: url('https://fonts.gstatic.com/s/rocksalt/v11/MwQ0bhv11fDH6wL6ZCL4I8Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Shadows Into Light';
  src: url('https://fonts.gstatic.com/s/shadowsintolight/v12/UqyNK9UOIntux_czAvDQx_ZcHqZXBNQDcg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Special Elite';
  src: url('https://fonts.gstatic.com/s/specialelite/v11/XLYgIZbkc4JPUL5CVArUVL0ntnAOTQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Walter Turncoat';
  src: url('https://fonts.gstatic.com/s/walterturncoat/v12/snfys0Gs98ln43n0d-14ULoToe67YB2M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط العربية الجديدة */
@font-face {
  font-family: 'Scheherazade New';
  src: url('https://fonts.gstatic.com/s/scheherazadenew/v4/4UaBrE6tmq0gO-tVs9Ipr5-9-mbRvNUF2I.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lateef';
  src: url('https://fonts.gstatic.com/s/lateef/v17/hESw6XVnNCxEvkbMpheEZo_H_w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Harmattan';
  src: url('https://fonts.gstatic.com/s/harmattan/v8/gokpH6L2DkFvVvRp9XpTS0CjkP1Yog.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'El Messiri';
  src: url('https://fonts.gstatic.com/s/elmessiri/v12/K2F0fZBRmr9vQ1pHEey6GIGo8_pv3myYjuXCe65ghj3OTw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Aref Ruqaa';
  src: url('https://fonts.gstatic.com/s/arefruqaa/v12/WwkbxPW1E165DjQ5VsZzqN5NjF7Nw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Readex Pro';
  src: url('https://fonts.gstatic.com/s/readexpro/v1/SLXYc1bJ7HE5YDoGPuzj_dh8na74Kiw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Sans Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexsansarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Arabic';
  src: url('https://fonts.gstatic.com/s/notosansarabic/v18/nwpCt6W9KfF6gV1yPu9T3JqRBNbE8tq1lx20.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Arabic';
  src: url('https://fonts.gstatic.com/s/notoserifarabic/v18/ga6Iaw1J5X9T9RW6j9bNVlsKbJovrb0b8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alkalami';
  src: url('https://fonts.gstatic.com/s/alkalami/v1/zOL-4pbPn6Im26Ke4HOxT7Y.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anek Arabic';
  src: url('https://fonts.gstatic.com/s/anekarabic/v1/5aUz9_-1phKLFgshYDvh6O4hp3wSa0b4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexserifarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Nastaliq Urdu';
  src: url('https://fonts.gstatic.com/s/notonastaliqurdu/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Rashi Hebrew';
  src: url('https://fonts.gstatic.com/s/notorashihebrew/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Hebrew';
  src: url('https://fonts.gstatic.com/s/notosanshebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Hebrew';
  src: url('https://fonts.gstatic.com/s/notoserifhebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Devanagari';
  src: url('https://fonts.gstatic.com/s/notosansdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Devanagari';
  src: url('https://fonts.gstatic.com/s/notoserifdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Bengali';
  src: url('https://fonts.gstatic.com/s/notosansbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Bengali';
  src: url('https://fonts.gstatic.com/s/notoserifbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Thai';
  src: url('https://fonts.gstatic.com/s/notosansthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Thai';
  src: url('https://fonts.gstatic.com/s/notoserifthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Korean';
  src: url('https://fonts.gstatic.com/s/notosanskorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Korean';
  src: url('https://fonts.gstatic.com/s/notoserifkorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Japanese';
  src: url('https://fonts.gstatic.com/s/notosansjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Japanese';
  src: url('https://fonts.gstatic.com/s/notoserifjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* إصلاح عنوان Text Assistant في الوضع المظلم */
.text-assistant h4 {
  color: #ffffff !important;
}

/* إصلاح حاوية التعديلات في الوضع المظلم */
.dark .text-assistant .custom-styling-container {
  background: linear-gradient(to right, rgba(17, 24, 39, 0.9), rgba(31, 41, 55, 0.8)) !important;
  border-color: rgba(55, 65, 81, 0.6) !important;
}

/* إصلاح حاوية التعديلات في الوضع الفاتح */
.text-assistant .custom-styling-container {
  background: linear-gradient(to right, rgba(239, 246, 255, 0.8), rgba(238, 242, 255, 0.8)) !important;
  border-color: rgba(191, 219, 254, 0.5) !important;
}

/* إصلاح حاوية التعليمات لتكون أغمق */
.dark .text-assistant .instructions-panel {
  background: linear-gradient(to bottom right, rgba(17, 24, 39, 0.9), rgba(31, 41, 55, 0.8), rgba(55, 65, 81, 0.7)) !important;
  border-color: rgba(55, 65, 81, 0.6) !important;
}

/* إصلاح إضافي للعنوان */
.text-assistant h4[style*="color"] {
  color: #ffffff !important;
}

/* إصلاح إضافي للحاوية */
.dark .text-assistant .custom-styling-container[class*="bg-gradient-to-r"] {
  background: linear-gradient(to right, rgba(31, 41, 55, 0.8), rgba(55, 65, 81, 0.8)) !important;
}

/* تأثيرات hover احترافية */
.text-assistant .p-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.text-assistant .p-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.text-assistant .p-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
}

/* تأثيرات hover للأزرار المحددة */
.text-assistant .p-button.p-button-outlined:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1)) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
  color: #3b82f6 !important;
}

/* تأثيرات hover للعناصر التفاعلية */
.text-assistant .p-dropdown:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12) !important;
}

.text-assistant .p-inputtext:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.text-assistant input[type="color"]:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15) !important;
}

/* تأثيرات hover للشرائح */
.text-assistant .p-slider:hover .p-slider-handle {
  transform: scale(1.2) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

/* تأثيرات hover للعناصر المخصصة */
.text-assistant .instructions-panel:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
}

.text-assistant .preview-area:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

/* إصلاح حدود dropdown في الوضع المظلم */
.text-assistant .p-dropdown {
  border: 1px solid rgba(75, 85, 99, 0.7) !important;
  background-color: rgba(31, 41, 55, 0.8) !important;
}

.text-assistant .p-dropdown:not(.p-disabled):hover {
  border-color: rgba(75, 85, 99, 0.9) !important;
}

.text-assistant .p-dropdown.p-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* إصلاح حدود InputText في الوضع المظلم */
.dark .text-assistant .p-inputtext {
  border: 1px solid rgba(75, 85, 99, 0.7) !important;
  background-color: rgba(31, 41, 55, 0.8) !important;
  color: #f3f4f6 !important;
}

.dark .text-assistant .p-inputtext:not(.p-disabled):hover {
  border-color: rgba(75, 85, 99, 0.9) !important;
}

.dark .text-assistant .p-inputtext:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* توحيد تأثيرات dropdown الخطوط مع باقي dropdowns */
.font-dropdown-panel {
  max-height: 300px !important;
  overflow-y: auto !important;
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 8px !important;
}

/* خلفية dropdown في الوضع المظلم */
.dark .font-dropdown-panel {
  background-color: rgba(17, 24, 39, 0.95) !important;
  border: 1px solid rgba(75, 85, 99, 0.7) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
}

.font-dropdown-panel .p-dropdown-items {
  max-height: 250px !important;
  background-color: transparent !important;
}

.font-dropdown-panel .p-dropdown-item {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  color: #374151 !important;
  transition: all 0.2s ease !important;
}

/* ألوان النصوص في الوضع المظلم */
.dark .font-dropdown-panel .p-dropdown-item {
  color: #f3f4f6 !important;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3) !important;
}

.font-dropdown-panel .p-dropdown-item:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
  transform: translateX(2px) !important;
  border-left: 3px solid #3b82f6 !important;
}

.font-dropdown-panel .p-dropdown-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3) !important;
}

/* إصلاح قوائم dropdown في الوضع المظلم */
.text-assistant .p-dropdown-panel {
  background-color: rgba(17, 24, 39, 0.95) !important;
  border: 1px solid rgba(75, 85, 99, 0.7) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

.text-assistant .p-dropdown-items {
  background-color: transparent !important;
}

.text-assistant .p-dropdown-item {
  color: #f3f4f6 !important;
  border-bottom: 1px solid rgba(75, 85, 99, 0.3) !important;
}

.text-assistant .p-dropdown-item:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
  border-left: 3px solid #3b82f6 !important;
}

.text-assistant .p-dropdown-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* إصلاح حقل البحث في dropdown */
.text-assistant .p-dropdown-filter {
  background-color: rgba(31, 41, 55, 0.8) !important;
  border-bottom: 1px solid rgba(75, 85, 99, 0.5) !important;
}

.text-assistant .p-dropdown-filter-input {
  background-color: rgba(31, 41, 55, 0.8) !important;
  border: 1px solid rgba(75, 85, 99, 0.7) !important;
  color: #f3f4f6 !important;
}

.text-assistant .p-dropdown-filter-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* إصلاح ألوان النصوص في Custom Styling */
.dark .text-assistant .custom-styling-container label {
  color: #ffffff !important;
}

.dark .text-assistant .custom-styling-container .text-white {
  color: #ffffff !important;
}

/* إصلاح ألوان النصوص في الوضع الفاتح */
.text-assistant .custom-styling-container label {
  color: #374151 !important;
}

/* إصلاح ألوان الشرائح */
.dark .text-assistant .custom-styling-container .p-slider {
  color: #ffffff !important;
}

.dark .text-assistant .custom-styling-container .p-slider .p-slider-label {
  color: #ffffff !important;
}

/* توحيد مظهر البحث في قائمة الخطوط مع باقي dropdowns */
.font-dropdown-panel .p-dropdown-filter {
  padding: 0.5rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  margin-bottom: 0.5rem !important;
  background-color: #ffffff !important;
}

/* خلفية حقل البحث في الوضع المظلم */
.dark .font-dropdown-panel .p-dropdown-filter {
  border-bottom: 1px solid rgba(75, 85, 99, 0.5) !important;
  background-color: rgba(31, 41, 55, 0.8) !important;
}

.font-dropdown-panel .p-dropdown-filter-input {
  width: 100% !important;
  padding: 0.5rem !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
  background-color: #ffffff !important;
  color: #374151 !important;
  transition: all 0.2s ease !important;
}

/* ألوان حقل البحث في الوضع المظلم */
.dark .font-dropdown-panel .p-dropdown-filter-input {
  border: 1px solid rgba(75, 85, 99, 0.7) !important;
  background-color: rgba(31, 41, 55, 0.8) !important;
  color: #f3f4f6 !important;
}

.font-dropdown-panel .p-dropdown-filter-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: #ffffff !important;
}

/* تأثيرات focus في الوضع المظلم */
.dark .font-dropdown-panel .p-dropdown-filter-input:focus {
  background-color: rgba(31, 41, 55, 0.9) !important;
}

.font-dropdown-panel .p-dropdown-filter-input:hover {
  border-color: #9ca3af !important;
}

/* تأثير hover في الوضع المظلم */
.dark .font-dropdown-panel .p-dropdown-filter-input:hover {
  border-color: rgba(75, 85, 99, 0.9) !important;
}

.font-dropdown-panel .p-virtualscroller {
  max-height: 200px !important;
}

.font-dropdown-panel .p-virtualscroller-content {
  padding: 0 !important;
}

.font-dropdown-panel .p-dropdown-item span:last-child {
  font-size: 0.75rem !important;
  opacity: 0.7 !important;
  margin-left: 0.5rem !important;
}

.text-assistant .p-dropdown.p-component {
  transition: all 0.2s ease !important;
}

.text-assistant .p-dropdown.p-component:not(.p-disabled):hover {
  border-color: #3b82f6 !important;
}

.text-assistant .p-dropdown.p-component.p-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* تأثيرات hover خاصة بـ dropdown الخطوط */
.text-assistant .p-dropdown[class*="font"]:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12) !important;
}

.text-assistant .p-dropdown[class*="font"].p-focus {
  transform: translateY(-1px) !important;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.15) !important;
}

/* تأثيرات خاصة بـ dropdown الخطوط في الوضع المظلم */
.dark .text-assistant .p-dropdown[class*="font"] {
  background-color: rgba(31, 41, 55, 0.8) !important;
  border: 1px solid rgba(75, 85, 99, 0.7) !important;
  color: #f3f4f6 !important;
}

.dark .text-assistant .p-dropdown[class*="font"]:not(.p-disabled):hover {
  border-color: rgba(75, 85, 99, 0.9) !important;
  background-color: rgba(31, 41, 55, 0.9) !important;
}

.dark .text-assistant .p-dropdown[class*="font"].p-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  background-color: rgba(31, 41, 55, 0.9) !important;
}
`;

// إضافة الـ styles إلى الـ document
if (typeof document !== 'undefined') {
    const styleId = 'text-assistant-styles';
    let existingStyle = document.getElementById(styleId);
    
    if (!existingStyle) {
        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = textAssistantStyles;
        document.head.appendChild(styleElement);
    }
}