// Performance Monitoring Utility for SideMenu
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      fps: 0,
      frameTime: 0,
      memoryUsage: 0,
      animationCount: 0,
      lastFrameTime: 0
    };
    this.isMonitoring = false;
    this.frameCount = 0;
    this.startTime = 0;
  }

  // Start performance monitoring
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.startTime = performance.now();
    this.frameCount = 0;
    
    // Monitor FPS
    this.monitorFPS();
    
    // Monitor memory usage
    this.monitorMemory();
    
    // Monitor animation performance
    this.monitorAnimations();
    
    console.log('🚀 Performance monitoring started');
  }

  // Stop performance monitoring
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('⏹️ Performance monitoring stopped');
    this.logMetrics();
  }

  // Monitor FPS
  monitorFPS() {
    if (!this.isMonitoring) return;

    const now = performance.now();
    this.frameCount++;
    
    if (now - this.startTime >= 1000) {
      this.metrics.fps = Math.round((this.frameCount * 1000) / (now - this.startTime));
      this.frameCount = 0;
      this.startTime = now;
      
      // Log FPS if it's low
      if (this.metrics.fps < 50) {
        console.warn(`⚠️ Low FPS detected: ${this.metrics.fps}fps`);
      }
    }

    requestAnimationFrame(() => this.monitorFPS());
  }

  // Monitor memory usage
  monitorMemory() {
    if (!this.isMonitoring) return;

    if (performance.memory) {
      this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
      
      // Log memory usage if it's high
      if (this.metrics.memoryUsage > 100) {
        console.warn(`⚠️ High memory usage: ${this.metrics.memoryUsage}MB`);
      }
    }

    setTimeout(() => this.monitorMemory(), 2000);
  }

  // Monitor animations
  monitorAnimations() {
    if (!this.isMonitoring) return;

    // Count active animations only in SideMenu
    const sidebarElement = document.querySelector('aside');
    let animationCount = 0;
    
    if (sidebarElement) {
      // Only count elements with actual CSS animations (not transitions)
      const animatedElements = sidebarElement.querySelectorAll('[style*="animation"]');
      animationCount = Array.from(animatedElements).filter(el => {
        const style = el.style.animation;
        return style && style !== 'none' && !style.includes('transition');
      }).length;
    }
    
    this.metrics.animationCount = animationCount;
    
    // Log if too many animations are running
    if (this.metrics.animationCount > 3) {
      console.warn(`⚠️ Too many animations running: ${this.metrics.animationCount}`);
    }

    setTimeout(() => this.monitorAnimations(), 1000);
  }

  // Get current metrics
  getMetrics() {
    return { ...this.metrics };
  }

  // Log performance metrics
  logMetrics() {
    console.group('📊 Performance Metrics');
    console.log(`🎯 FPS: ${this.metrics.fps}`);
    console.log(`🧠 Memory: ${this.metrics.memoryUsage}MB`);
    console.log(`🎬 Animations: ${this.metrics.animationCount}`);
    console.log(`⏱️ Frame Time: ${this.metrics.frameTime}ms`);
    console.groupEnd();
  }

  // Performance recommendations
  getRecommendations() {
    const recommendations = [];
    
    if (this.metrics.fps < 50) {
      recommendations.push('Consider reducing animation complexity');
    }
    
    if (this.metrics.memoryUsage > 100) {
      recommendations.push('Consider optimizing memory usage');
    }
    
    if (this.metrics.animationCount > 10) {
      recommendations.push('Consider staggering animations');
    }
    
    return recommendations;
  }
}

// Create global instance
const performanceMonitor = new PerformanceMonitor();

// Export for use in components
export default performanceMonitor;

// Development helper functions
export const devHelpers = {
  // Start monitoring in development
  startDevMonitoring() {
    if (import.meta.env.DEV) {
      performanceMonitor.startMonitoring();
      
      // Add to window for easy access
      window.performanceMonitor = performanceMonitor;
      
      console.log('🔧 Development performance monitoring enabled');
      console.log('💡 Use window.performanceMonitor to access metrics');
    }
  },
  
  // Get performance report
  getReport() {
    const metrics = performanceMonitor.getMetrics();
    const recommendations = performanceMonitor.getRecommendations();
    
    return {
      metrics,
      recommendations,
      timestamp: new Date().toISOString()
    };
  },
  
  // Benchmark function
  benchmark(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    console.log(`⏱️ ${name}: ${(end - start).toFixed(2)}ms`);
    return result;
  }
};