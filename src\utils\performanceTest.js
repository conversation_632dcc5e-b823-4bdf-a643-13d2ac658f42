// Performance Testing Utilities
export const performanceTest = {
  // Test animation performance
  testAnimationPerformance() {
    console.group('🎬 Animation Performance Test');
    
    const startTime = performance.now();
    const animatedElements = document.querySelectorAll('[style*="animation"]');
    const endTime = performance.now();
    
    console.log(`⏱️ Found ${animatedElements.length} animated elements in ${(endTime - startTime).toFixed(2)}ms`);
    
    // Test each animation
    animatedElements.forEach((element, index) => {
      const computedStyle = window.getComputedStyle(element);
      const animationName = computedStyle.animationName;
      const animationDuration = computedStyle.animationDuration;
      const animationDelay = computedStyle.animationDelay;
      
      console.log(`🎭 Animation ${index + 1}:`, {
        element: element.tagName,
        name: animationName,
        duration: animationDuration,
        delay: animationDelay
      });
    });
    
    console.groupEnd();
  },

  // Test FPS
  testFPS(duration = 5000) {
    console.group('🎯 FPS Test');
    
    let frameCount = 0;
    let startTime = performance.now();
    
    const measureFrame = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - startTime < duration) {
        requestAnimationFrame(measureFrame);
      } else {
        const fps = Math.round((frameCount * 1000) / duration);
        console.log(`📊 Average FPS over ${duration/1000}s: ${fps}`);
        
        if (fps >= 55) {
          console.log('✅ Excellent performance!');
        } else if (fps >= 45) {
          console.log('⚠️ Good performance, but could be better');
        } else {
          console.log('❌ Poor performance, needs optimization');
        }
        
        console.groupEnd();
      }
    };
    
    requestAnimationFrame(measureFrame);
  },

  // Test memory usage
  testMemoryUsage() {
    console.group('🧠 Memory Usage Test');
    
    if (performance.memory) {
      const memory = performance.memory;
      console.log('📊 Memory Usage:', {
        used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
        total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
        limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`
      });
      
      const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
      console.log(`📈 Memory Usage: ${usagePercent.toFixed(1)}%`);
      
      if (usagePercent < 50) {
        console.log('✅ Memory usage is healthy');
      } else if (usagePercent < 80) {
        console.log('⚠️ Memory usage is moderate');
      } else {
        console.log('❌ Memory usage is high');
      }
    } else {
      console.log('❌ Memory API not available');
    }
    
    console.groupEnd();
  },

  // Test rendering performance
  testRenderingPerformance() {
    console.group('🎨 Rendering Performance Test');
    
    const startTime = performance.now();
    
    // Force a reflow
    document.body.offsetHeight;
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    console.log(`⏱️ Reflow time: ${renderTime.toFixed(2)}ms`);
    
    if (renderTime < 16) {
      console.log('✅ Excellent rendering performance');
    } else if (renderTime < 33) {
      console.log('⚠️ Good rendering performance');
    } else {
      console.log('❌ Poor rendering performance');
    }
    
    console.groupEnd();
  },

  // Run all performance tests
  runAllTests() {
    console.group('🚀 Running All Performance Tests');
    
    this.testAnimationPerformance();
    this.testMemoryUsage();
    this.testRenderingPerformance();
    this.testFPS();
    
    console.groupEnd();
  },

  // Benchmark function
  benchmark(name, fn, iterations = 1000) {
    console.group(`⏱️ Benchmark: ${name}`);
    
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      fn();
      const end = performance.now();
      times.push(end - start);
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`📊 Results (${iterations} iterations):`, {
      average: `${avgTime.toFixed(3)}ms`,
      minimum: `${minTime.toFixed(3)}ms`,
      maximum: `${maxTime.toFixed(3)}ms`
    });
    
    console.groupEnd();
    
    return { avgTime, minTime, maxTime };
  }
};

// Development helpers
export const devPerformanceHelpers = {
  // Add to window for easy access
  init() {
    if (import.meta.env.DEV) {
      window.performanceTest = performanceTest;
      console.log('🔧 Performance testing tools available at window.performanceTest');
      console.log('💡 Try: window.performanceTest.runAllTests()');
    }
  },

  // Quick performance check
  quickCheck() {
    console.log('⚡ Quick Performance Check');
    performanceTest.testMemoryUsage();
    performanceTest.testFPS(2000);
  }
};
