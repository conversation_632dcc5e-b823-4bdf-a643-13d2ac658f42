/* Location Picker Styles */
.location-picker {
  font-family: inherit;
}

/* Location Picker Field Styles */
.location-picker-field {
  width: 100%;
}

.location-display-container {
  position: relative;
}

.location-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 60px;
}

.location-input:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.location-input:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 6px rgba(0, 0, 0, 0.1);
}

.location-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.location-input.has-value {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.location-input-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.location-icon {
  width: 24px;
  height: 24px;
  color: #6b7280;
  flex-shrink: 0;
}

.location-input.has-value .location-icon {
  color: #10b981;
}

.location-text {
  flex: 1;
  min-width: 0;
}

.selected-location {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.location-address {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-words;
}

.location-coordinates {
  font-size: 12px;
  color: #6b7280;
  font-family: 'Courier New', monospace;
}

.placeholder-text {
  color: #9ca3af;
  font-size: 14px;
  font-style: italic;
}

.select-location-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.select-location-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.select-location-btn svg {
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease;
}

.location-input:hover .select-location-btn svg {
  transform: translateY(2px);
}

/* Selected Location Display */
.selected-location-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border: 2px solid #10b981;
  border-radius: 12px;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.1);
}

.location-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.location-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.change-location-btn {
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.change-location-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.select-location-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.select-location-btn:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.1);
}

.location-btn-icon {
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.select-location-btn:hover .location-btn-icon {
  transform: scale(1.1);
}

/* Field Instructions */
.field-instructions {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  font-style: italic;
}

/* Error Message */
.location-picker-field .error-message {
  color: #ef4444;
  font-size: 14px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: fadeIn 0.3s ease-in;
}

.location-picker-field .error-message i {
  color: #ef4444;
  font-size: 16px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.location-picker .leaflet-container {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.location-picker .leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.location-picker .leaflet-popup-content {
  margin: 8px 12px;
  font-size: 14px;
}

.location-picker .leaflet-popup-tip {
  background: white;
}

/* Search Results Styling */
.location-picker .search-results {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.location-picker .search-result-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.location-picker .search-result-item:hover {
  background-color: #f9fafb;
}

.location-picker .search-result-item:last-child {
  border-bottom: none;
}

.location-picker .search-result-item .result-title {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.location-picker .search-result-item .result-subtitle {
  font-size: 12px;
  color: #6b7280;
}

/* Button Styling */
.location-picker .current-location-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

/* Error State for Location Picker */
.location-picker-field .select-location-btn.error-state {
  border: 2px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.location-picker-field .select-location-btn.error-state:hover {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.location-picker-field .select-location-btn.error-state i {
  color: #dc2626;
}

.location-picker .current-location-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.location-picker .current-location-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.location-picker .search-btn {
  padding: 10px 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.location-picker .search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.location-picker .search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Input Styling */
.location-picker .search-input {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.location-picker .search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Address Display */
.location-picker .address-display {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.location-picker .address-display .address-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.location-picker .address-display .address-text {
  color: #4b5563;
  line-height: 1.5;
  word-break: break-words;
  font-size: 14px;
}

.location-picker .address-display .coordinates {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  font-family: 'Courier New', monospace;
}

/* Instructions */
.location-picker .instructions {
  margin-top: 12px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  font-style: italic;
}

/* Error Styling */
.location-picker .error-message {
  color: #dc2626;
  font-size: 14px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
}

/* Loading States */
.location-picker .loading {
  opacity: 0.7;
  pointer-events: none;
}

.location-picker .loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Modal Styles */
.location-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.location-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 95vw;
  max-height: 95vh;
  width: 1200px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to { 
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  width: 24px;
  height: 24px;
  color: #3b82f6;
}

.modal-title h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.close-btn svg {
  width: 20px;
  height: 20px;
}

/* Modal Content */
.modal-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: calc(95vh - 200px);
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Search Section */
.search-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.location-search-container {
  width: 100%;
}

.search-form {
  display: flex;
  gap: 12px;
  align-items: stretch;
  width: 100%;
}

.search-form .search-input-wrapper {
  flex: 1;
  min-width: 0;
}

.search-form .search-btn {
  flex-shrink: 0;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  min-width: 0;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 14px 16px 14px 44px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  height: 48px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-btn {
  padding: 14px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
  height: 48px;
  box-sizing: border-box;
}

.search-btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.search-btn .loading-spinner {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

.search-btn span {
  flex-shrink: 0;
}

/* Search Error */
.search-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin-top: 8px;
  color: #dc2626;
  font-size: 14px;
}

.search-error .error-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Search Tips */
.search-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 6px;
  margin-top: 8px;
  color: #0369a1;
  font-size: 12px;
}

.search-tips .info-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

/* Location Error */
.location-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin-top: 8px;
  color: #dc2626;
  font-size: 14px;
}

.location-error .error-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(59, 130, 246, 0.3);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  min-width: 120px;
}

/* Search Results */
.search-results {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 8px;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-result-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.result-icon svg {
  width: 20px;
  height: 20px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.4;
}

.result-subtitle {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.3;
}

/* Current Location Button */
.current-location-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
  width: 100%;
}

.current-location-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(16, 185, 129, 0.3);
}

.current-location-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.current-location-btn .loading-spinner {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

.current-location-btn svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}



/* Map Container */
.map-container {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  min-height: 500px;
  height: 500px;
}

.map {
  height: 500px !important;
  width: 100%;
  min-height: 500px;
}

/* Address Display */
.address-display {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.address-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.address-icon {
  width: 24px;
  height: 24px;
  color: #3b82f6;
}

.address-label {
  font-weight: 600;
  color: #374151;
  font-size: 16px;
}

.address-text {
  color: #4b5563;
  line-height: 1.6;
  font-size: 16px;
  margin-bottom: 12px;
  word-break: break-words;
}

.coordinates {
  font-size: 14px;
  color: #6b7280;
  font-family: 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border-radius: 8px;
  display: inline-block;
}

/* Instructions */
.instructions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 12px;
  border: 1px solid #f59e0b;
  color: #92400e;
  font-size: 14px;
  font-weight: 500;
}

.info-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.cancel-btn {
  flex: 1;
  padding: 14px 24px;
  background: white;
  color: #6b7280;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  color: #374151;
}

.confirm-btn {
  flex: 1;
  padding: 14px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(59, 130, 246, 0.3);
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Loading Spinner */
.loading-spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 1.5px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  flex-shrink: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Popup Content */
.popup-content {
  text-align: center;
  padding: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .location-picker .leaflet-container {
    height: 300px !important;
  }
  
  .location-picker .search-form {
    flex-direction: column;
    gap: 8px;
  }
  
  .location-picker .search-input,
  .location-picker .search-btn {
    width: 100%;
  }
  
  .location-picker .current-location-btn {
    width: 100%;
    justify-content: center;
  }

  .location-modal {
    max-width: 95vw;
    max-height: 95vh;
    margin: 10px;
  }
  
  .modal-header {
    padding: 20px 24px;
  }
  
  .modal-content {
    padding: 24px;
    gap: 20px;
  }
  
  .search-form {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-input,
  .search-btn {
    width: 100%;
  }
  
  .map {
    height: 400px !important;
    min-height: 400px;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 12px;
  }
  
  .cancel-btn,
  .confirm-btn {
    width: 100%;
  }
}

/* RTL Support */
[dir="rtl"] .location-picker .search-form {
  flex-direction: row-reverse;
}

[dir="rtl"] .location-picker .current-location-btn {
  flex-direction: row-reverse;
}

/* Dark Mode Support */
.dark .location-picker,
.dark .location-modal-overlay,
.dark .location-modal {
  /* Location Input Field */
  .location-input {
    background: var(--surface) !important;
    border-color: var(--border) !important;
    color: var(--text-primary) !important;
  }

  .location-input:hover {
    border-color: var(--accent) !important;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.1) !important;
  }

  .location-input:focus-within {
    border-color: var(--accent) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  .location-input.has-value {
    border-color: #10b981 !important;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%) !important;
  }

  .location-icon {
    color: var(--text-secondary) !important;
  }

  .location-input.has-value .location-icon {
    color: #10b981 !important;
  }

  .location-address {
    color: var(--text-primary) !important;
  }

  .location-coordinates {
    color: var(--text-secondary) !important;
  }

  .placeholder-text {
    color: var(--text-tertiary) !important;
  }

  .select-location-btn {
    color: var(--text-secondary) !important;
  }

  .select-location-btn:hover {
    background: var(--surface-hover) !important;
    color: var(--text-primary) !important;
  }

  /* Selected Location Display */
  .selected-location-display {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%) !important;
    border-color: #10b981 !important;
  }

  .change-location-btn {
    background: #10b981 !important;
    color: white !important;
  }

  .change-location-btn:hover {
    background: #059669 !important;
  }

  /* Select Location Button */
  .select-location-btn {
    background: var(--surface) !important;
    border-color: var(--border) !important;
    color: var(--text-secondary) !important;
  }

  .select-location-btn:hover {
    border-color: var(--accent) !important;
    background: var(--surface-hover) !important;
    color: var(--text-primary) !important;
  }

  /* Error States */
  .location-input.error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
  }

  .select-location-btn.error-state {
    border-color: #ef4444 !important;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%) !important;
    color: #dc2626 !important;
  }

  .select-location-btn.error-state:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%) !important;
  }

  /* Error Message */
  .error-message {
    background: rgba(239, 68, 68, 0.1) !important;
    border-color: rgba(239, 68, 68, 0.3) !important;
    color: #ef4444 !important;
  }
}

/* Modal Dark Mode */
.dark .location-modal-overlay {
  background: rgba(0, 0, 0, 0.8) !important;
}

.dark .location-modal {
  background: var(--surface) !important;
  border: 1px solid var(--border) !important;
}

.dark .modal-header {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
  border-bottom-color: var(--border) !important;
}

.dark .modal-title h2 {
  color: var(--text-primary) !important;
}

.dark .title-icon {
  color: var(--accent) !important;
}

.dark .close-btn {
  color: var(--text-secondary) !important;
}

.dark .close-btn:hover {
  background: var(--surface-hover) !important;
  color: var(--text-primary) !important;
}

.dark .modal-content {
  background: var(--surface) !important;
}

.dark .modal-content::-webkit-scrollbar-track {
  background: var(--bg-secondary) !important;
}

.dark .modal-content::-webkit-scrollbar-thumb {
  background: var(--border) !important;
}

.dark .modal-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary) !important;
}

/* Search Section Dark Mode */
.dark .search-input {
  background: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .search-input:focus {
  border-color: var(--accent) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.dark .search-input::placeholder {
  color: var(--text-tertiary) !important;
}

.dark .search-icon {
  color: var(--text-tertiary) !important;
}

.dark .search-btn {
  background: linear-gradient(135deg, var(--accent) 0%, #2563eb 100%) !important;
  color: white !important;
}

.dark .search-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
}

.dark .search-btn:disabled {
  opacity: 0.6 !important;
}

/* Search Error Dark Mode */
.dark .search-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
}

.dark .search-tips {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  color: var(--accent) !important;
}

.dark .location-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: #ef4444 !important;
}

/* Search Results Dark Mode */
.dark .search-results {
  background: var(--surface) !important;
  border-color: var(--border) !important;
}

.dark .search-result-item {
  background: var(--surface) !important;
  border-bottom-color: var(--border) !important;
  color: var(--text-primary) !important;
}

.dark .search-result-item:hover {
  background: var(--surface-hover) !important;
}

.dark .result-title {
  color: var(--text-primary) !important;
}

.dark .result-subtitle {
  color: var(--text-secondary) !important;
}

.dark .result-icon {
  background: linear-gradient(135deg, var(--accent) 0%, #2563eb 100%) !important;
}

/* Current Location Button Dark Mode */
.dark .current-location-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
}

.dark .current-location-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
}

.dark .current-location-btn:disabled {
  opacity: 0.6 !important;
}

/* Map Container Dark Mode */
.dark .map-container {
  border-color: var(--border) !important;
}

/* Address Display Dark Mode */
.dark .address-display {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
  border-color: var(--border) !important;
}

.dark .address-label {
  color: var(--text-primary) !important;
}

.dark .address-text {
  color: var(--text-secondary) !important;
}

.dark .coordinates {
  background: rgba(0, 0, 0, 0.1) !important;
  color: var(--text-tertiary) !important;
}

/* Instructions Dark Mode */
.dark .instructions {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%) !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
  color: #f59e0b !important;
}

/* Modal Footer Dark Mode */
.dark .modal-footer {
  background: var(--bg-secondary) !important;
  border-top-color: var(--border) !important;
}

.dark .cancel-btn {
  background: var(--surface) !important;
  border-color: var(--border) !important;
  color: var(--text-secondary) !important;
}

.dark .cancel-btn:hover {
  background: var(--surface-hover) !important;
  border-color: var(--border-hover) !important;
  color: var(--text-primary) !important;
}

.dark .confirm-btn {
  background: linear-gradient(135deg, var(--accent) 0%, #2563eb 100%) !important;
  color: white !important;
}

.dark .confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
}

.dark .confirm-btn:disabled {
  opacity: 0.5 !important;
}

/* Popup Content Dark Mode */
.dark .popup-content {
  color: var(--text-primary) !important;
}

.dark .popup-content small {
  color: var(--text-secondary) !important;
}

/* Leaflet Map Dark Mode */
.dark .leaflet-container {
  background: var(--bg-secondary) !important;
}

.dark .leaflet-popup-content-wrapper {
  background: var(--surface) !important;
  color: var(--text-primary) !important;
}

.dark .leaflet-popup-tip {
  background: var(--surface) !important;
}

/* Loading States Dark Mode */
.dark .loading-spinner {
  border-color: var(--text-primary) !important;
  border-top-color: transparent !important;
}

/* Additional Dark Mode Improvements */
.dark .field-instructions {
  color: var(--text-secondary) !important;
}

.dark .location-picker .leaflet-container {
  filter: brightness(0.8) contrast(1.2);
}

/* Dark mode for loading indicators */
.dark .bg-gradient-to-r.from-blue-500\/20.to-purple-500\/20 {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%) !important;
}

.dark .bg-gradient-to-r.from-green-500\/20.to-emerald-500\/20 {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%) !important;
}

.dark .bg-gradient-to-r.from-blue-500\/20.to-indigo-500\/20 {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.2) 100%) !important;
}

.dark .text-blue-300 {
  color: #93c5fd !important;
}

.dark .text-blue-400 {
  color: #60a5fa !important;
}

.dark .text-green-300 {
  color: #86efac !important;
}

.dark .text-green-400 {
  color: #4ade80 !important;
}

/* Dark mode for border colors */
.dark .border-blue-400\/30 {
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.dark .border-green-400\/30 {
  border-color: rgba(16, 185, 129, 0.3) !important;
}

/* Dark mode for background colors */
.dark .bg-blue-500\/20 {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

.dark .bg-green-500\/20 {
  background-color: rgba(16, 185, 129, 0.2) !important;
}