import { useEffect, useState, useCallback, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { useRef } from 'react';

import { useDeleteGroupMutation } from '@quires'
import { groupsTableConfig, defaultTableConfig } from '@constants';
import { useGroupsDataTableContext } from '@contexts/GroupsDataTableContext';
import { useLayout } from '@contexts/LayoutContext';
import { useQueryClient } from 'react-query';

import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';

import { TfiTrash } from "react-icons/tfi";
import { FaRegEye } from 'react-icons/fa';
import { HiDotsVertical } from 'react-icons/hi';
import { FiEdit } from 'react-icons/fi';
import { FaPalette } from 'react-icons/fa';
import { FaUsers, FaBluetooth } from 'react-icons/fa';
import { FaIdCardAlt } from "react-icons/fa";
import { FaCreditCard } from "react-icons/fa";
import { FaPlus } from "react-icons/fa";
import { MdFilterList, MdCategory, MdTrendingUp, MdClear } from "react-icons/md";
import { BiGroup } from "react-icons/bi";
import { BsCardText, BsCheckCircle, BsXCircle } from "react-icons/bs";
import { LuNfc } from "react-icons/lu";


import { createPortal } from 'react-dom';
import GroupForm from '../../Backages/CreateGroupForm';
import Container from '@components/Container';
import axiosInstance from '../../../../config/Axios';
import { motion } from 'framer-motion';
import ImageGenerationModal from '../../DesignSpace/components/ImageGenerationModal';
import GroupMembersPage from '../Members/GroupMembersPage';
import { MembersDataTableProvider } from '@contexts/MembersDataTableContext';

// Import Avatar components
import { Avatar, AvatarFallback, AvatarImage } from '../../../../components/ui/avatar';
import React from 'react';

// CSS مخصص للـ dialog ملء الشاشة
const fullscreenMembersModalStyles = `
  /* إعدادات أساسية للـ dialog */
  .group-members-modal-fullscreen .p-dialog {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    margin: 0 !important;
    top: 0 !important;
    left: 0 !important;
    border-radius: 0 !important;
  }
  
  /* إعدادات الـ header */
  .group-members-modal-fullscreen .p-dialog-header {
    height: 60px !important;
    border-bottom: none !important;
    width: 100% !important;
  }
  
  /* إعدادات الـ content */
  .group-members-modal-fullscreen .p-dialog-content {
    height: calc(100vh - 60px) !important;
    min-height: calc(100vh - 60px) !important;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    overflow: hidden !important;
    border-bottom: none !important;
  }
  
  /* إعدادات الـ mask */
  .group-members-modal-fullscreen .p-dialog-mask {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }
  
  /* إزالة الـ footer تماماً */
  .group-members-modal-fullscreen .p-dialog-footer,
  .group-members-modal-fullscreen .p-dialog .p-dialog-footer,
  .group-members-modal-fullscreen .p-component .p-dialog-footer {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    width: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    overflow: hidden !important;
  }
  
  /* إزالة جميع عناصر الـ footer */
  .group-members-modal-fullscreen .p-dialog .p-dialog-footer *,
  .group-members-modal-fullscreen .p-dialog .p-dialog-footer::before,
  .group-members-modal-fullscreen .p-dialog .p-dialog-footer::after {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    width: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    overflow: hidden !important;
  }
  
  /* ضمان أن جميع العناصر الفرعية تأخذ كامل الحجم */
  .group-members-modal-fullscreen .p-dialog-content > div,
  .group-members-modal-fullscreen .p-dialog-content > div > div {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    height: calc(100vh - 60px) !important;
    min-height: calc(100vh - 60px) !important;
    max-height: calc(100vh - 60px) !important;
  }
  
  /* إعدادات خاصة للوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    margin: 0 !important;
    top: 0 !important;
    left: 0 !important;
    border-radius: 0 !important;
  }
  
  .dark .group-members-modal-fullscreen .p-dialog-content {
    background-color: #1f2937 !important;
    height: calc(100vh - 60px) !important;
    min-height: calc(100vh - 60px) !important;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    overflow: hidden !important;
  }
  
  /* Force full screen for group members modal - Override all conflicting styles */
  .group-members-modal-fullscreen,
  .group-members-modal-fullscreen.p-dialog,
  .p-dialog.group-members-modal-fullscreen,
  .p-dialog-mask .group-members-modal-fullscreen,
  .p-dialog-mask .group-members-modal-fullscreen.p-dialog {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    position: fixed !important;
    z-index: 9999 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  /* Override content area */
  .group-members-modal-fullscreen .p-dialog-content,
  .group-members-modal-fullscreen.p-dialog .p-dialog-content,
  .p-dialog.group-members-modal-fullscreen .p-dialog-content {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    background: transparent !important;
    border: none !important;
  }
  
  /* Override header */
  .group-members-modal-fullscreen .p-dialog-header,
  .group-members-modal-fullscreen.p-dialog .p-dialog-header,
  .p-dialog.group-members-modal-fullscreen .p-dialog-header {
    width: 100% !important;
    padding: 1rem !important;
    background-color: #ffffff !important;
    border-bottom: none !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }
  
  /* Override mask */
  .p-dialog-mask .group-members-modal-fullscreen,
  .p-dialog-mask .group-members-modal-fullscreen.p-dialog {
    background: transparent !important;
    backdrop-filter: none !important;
  }
  
  /* Dark mode overrides for group members modal */
  .dark .group-members-modal-fullscreen .p-dialog-header,
  .dark .group-members-modal-fullscreen.p-dialog .p-dialog-header,
  .dark .p-dialog.group-members-modal-fullscreen .p-dialog-header {
    background-color: #1f2937 !important;
    border-bottom: none !important;
  }
  
  .dark .group-members-modal-fullscreen .p-dialog-header .p-dialog-title,
  .dark .group-members-modal-fullscreen.p-dialog .p-dialog-header .p-dialog-title,
  .dark .p-dialog.group-members-modal-fullscreen .p-dialog-header .p-dialog-title {
    color: #f9fafb !important;
  }
  
  /* Close button hover effects */
  .group-members-modal-fullscreen .p-dialog-header button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* Close button specific styling */
  .group-members-modal-fullscreen .p-dialog-header button[title="Close Modal"]:hover {
    background-color: #fef2f2 !important;
    border-color: #fca5a5 !important;
    color: #dc2626 !important;
  }
  
  .dark .group-members-modal-fullscreen .p-dialog-header button[title="Close Modal"]:hover {
    background-color: rgba(127, 29, 29, 0.2) !important;
    border-color: #dc2626 !important;
    color: #fca5a5 !important;
  }
  
  /* Additional overrides to ensure full screen modals work */
  .p-dialog-mask {
    background-color: rgba(0, 0, 0, 0.4) !important;
  }
  
  /* Override any PrimeReact default styles that might conflict */
  .p-dialog.group-members-modal-fullscreen {
    transform: none !important;
    transition: none !important;
  }
  
  /* Exit Warning Modal Styles */
  .exit-warning-modal,
  .exit-warning-modal.p-dialog,
  .p-dialog.exit-warning-modal,
  .p-dialog-mask .exit-warning-modal,
  .p-dialog-mask .exit-warning-modal.p-dialog {
    width: 100vw !important;
    height: 100vh !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    position: fixed !important;
    z-index: 9999 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  /* Override exit warning modal content */
  .exit-warning-modal .p-dialog-content,
  .exit-warning-modal.p-dialog .p-dialog-content,
  .p-dialog.exit-warning-modal .p-dialog-content {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    border: none !important;
  }
  
  /* Override exit warning modal mask */
  .p-dialog-mask .exit-warning-modal,
  .p-dialog-mask .exit-warning-modal.p-dialog {
    background: transparent !important;
    backdrop-filter: none !important;
  }
  
  /* Dark mode overrides for exit warning modal */
  .dark .exit-warning-modal .p-dialog-content,
  .dark .exit-warning-modal.p-dialog .p-dialog-content,
  .dark .p-dialog.exit-warning-modal .p-dialog-content {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }
  
  /* Exit Warning Modal close button styling */
  .exit-warning-modal button[title="Close Modal"]:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    background-color: #fef2f2 !important;
    border-color: #fca5a5 !important;
    color: #dc2626 !important;
  }
  
  .dark .exit-warning-modal button[title="Close Modal"]:hover {
    background-color: rgba(127, 29, 29, 0.2) !important;
    border-color: #dc2626 !important;
    color: #fca5a5 !important;
  }
  
  .dark .group-members-modal-fullscreen .p-dialog-content > div,
  .dark .group-members-modal-fullscreen .p-dialog-content > div > div {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    height: calc(100vh - 60px) !important;
    min-height: calc(100vh - 60px) !important;
    max-height: calc(100vh - 60px) !important;
  }
  
  /* إعدادات إضافية لضمان العمل الصحيح في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog-content {
    max-width: 100% !important;
    max-height: calc(100vh - 60px) !important;
  }
  
  /* ضمان أن المحتوى الداخلي في الوضع المظلم يأخذ كامل الحجم */
  .dark .group-members-modal-fullscreen .p-dialog-content > div {
    background-color: #1f2937 !important;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    height: calc(100vh - 60px) !important;
    min-height: calc(100vh - 60px) !important;
    max-height: calc(100vh - 60px) !important;
  }
  
  /* ضمان أن GroupMembersPage يأخذ كامل الحجم في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog-content > div > div {
    background-color: #1f2937 !important;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    height: calc(100vh - 60px) !important;
    min-height: calc(100vh - 60px) !important;
    max-height: calc(100vh - 60px) !important;
  }
  
  /* إعدادات إضافية لضمان العرض الكامل في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog-content,
  .dark .group-members-modal-fullscreen .p-dialog-content > *,
  .dark .group-members-modal-fullscreen .p-dialog-content > * > *,
  .dark .group-members-modal-fullscreen .p-dialog-content > * > * > * {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* إزالة أي padding أو margin في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog-content > div {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* ضمان أن الـ header يأخذ كامل العرض في الوضع المظلم */
  .dark .group-members-modal-fullscreen .groups-header-container {
    border-radius: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* ضمان أن جميع العناصر الداخلية تأخذ كامل العرض */
  .dark .group-members-modal-fullscreen .w-full {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* ضمان أن الـ pagination يظهر بشكل صحيح في الـ modal */
  .group-members-modal-fullscreen .overflow-auto {
    padding-bottom: 100px !important;
    max-height: calc(100vh - 60px) !important;
    overflow-y: auto !important;
  }
  
  /* ضمان أن الـ pagination ثابت في أسفل المنطقة المرئية */
  .group-members-modal-fullscreen .overflow-auto::after {
    content: '';
    display: block;
    height: 100px;
    width: 100%;
  }
  
  /* تحسين عرض الـ pagination في الوضع المظلم */
  .dark .group-members-modal-fullscreen .overflow-auto {
    padding-bottom: 100px !important;
    max-height: calc(100vh - 60px) !important;
    overflow-y: auto !important;
  }
  
  /* ضمان أن الـ pagination يظهر بشكل صحيح في الوضع المظلم */
  .dark .group-members-modal-fullscreen .overflow-auto::after {
    content: '';
    display: block;
    height: 100px;
    width: 100%;
  }
  
  /* ضمان أن الـ pagination يظهر بشكل صحيح في جميع الحالات */
  .group-members-modal-fullscreen .space-y-4 {
    padding-bottom: 20px !important;
  }
  
  /* ضمان أن الـ pagination يظهر بشكل صحيح في الوضع المظلم */
  .dark .group-members-modal-fullscreen .space-y-4 {
    padding-bottom: 20px !important;
  }
  
  /* تحسين عرض الـ pagination الخاص بالـ DataTable في الـ modal */
  .group-members-modal-fullscreen .p-datatable .p-paginator {
    padding: 1rem !important;
    background: transparent !important;
    border: none !important;
    margin-top: 1rem !important;
  }
  
  /* تحسين عرض الـ pagination الخاص بالـ DataTable في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator {
    padding: 1rem !important;
    background: transparent !important;
    border: none !important;
    margin-top: 1rem !important;
  }
  
  /* ضمان أن الـ DataTable يأخذ كامل العرض */
  .group-members-modal-fullscreen .p-datatable {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* ضمان أن الـ DataTable يأخذ كامل العرض في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-datatable {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* ضمان أن الـ pagination الخاص بالـ DataTable يظهر بشكل صحيح */
  .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-pages {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
  }
  
  /* ضمان أن الـ pagination الخاص بالـ DataTable يظهر بشكل صحيح في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-pages {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
  }
  
  /* تحسين عرض أزرار الـ pagination */
  .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-page,
  .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-first,
  .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-prev,
  .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-next,
  .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-last {
    margin: 0.25rem !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
  }
  
  /* تحسين عرض أزرار الـ pagination في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-page,
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-first,
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-prev,
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-next,
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-last {
    margin: 0.25rem !important;
    min-width: 2.5rem !important;
    height: 2.5rem !important;
  }
  
  /* ضمان أن الـ pagination الخاص بالـ DataTable يظهر بشكل صحيح */
  .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-current {
    margin: 0 1rem !important;
    padding: 0.5rem 1rem !important;
  }
  
  /* ضمان أن الـ pagination الخاص بالـ DataTable يظهر بشكل صحيح في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-datatable .p-paginator .p-paginator-current {
    margin: 0 1rem !important;
    padding: 0.5rem 1rem !important;
  }
  
  /* إزالة الخط أسفل الـ modal بشكل نهائي */
  .group-members-modal-fullscreen .p-dialog-header,
  .group-members-modal-fullscreen .p-dialog .p-dialog-header,
  .p-dialog.group-members-modal-fullscreen .p-dialog-header,
  .group-members-modal-fullscreen .p-dialog-header::after,
  .group-members-modal-fullscreen .p-dialog-header::before {
    border-bottom: none !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  /* إزالة الخط أسفل الـ modal في الوضع المظلم بشكل نهائي */
  .dark .group-members-modal-fullscreen .p-dialog-header,
  .dark .group-members-modal-fullscreen .p-dialog .p-dialog-header,
  .dark .p-dialog.group-members-modal-fullscreen .p-dialog-header,
  .dark .group-members-modal-fullscreen .p-dialog-header::after,
  .dark .group-members-modal-fullscreen .p-dialog-header::before {
    border-bottom: none !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  /* إزالة أي خط من الـ content أو العناصر الأخرى */
  .group-members-modal-fullscreen .p-dialog-content,
  .group-members-modal-fullscreen .p-dialog-content::before,
  .group-members-modal-fullscreen .p-dialog-content::after {
    border-top: none !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  /* إزالة أي خط من الـ content في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog-content,
  .dark .group-members-modal-fullscreen .p-dialog-content::before,
  .dark .group-members-modal-fullscreen .p-dialog-content::after {
    border-top: none !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  /* إزالة جميع الخطوط من الـ modal بشكل نهائي */
  .group-members-modal-fullscreen .p-dialog,
  .group-members-modal-fullscreen .p-dialog *,
  .group-members-modal-fullscreen .p-dialog *::before,
  .group-members-modal-fullscreen .p-dialog *::after {
    border: none !important;
    border-top: none !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
  
  /* إزالة جميع الخطوط من الـ modal في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog,
  .dark .group-members-modal-fullscreen .p-dialog *,
  .dark .group-members-modal-fullscreen .p-dialog *::before,
  .dark .group-members-modal-fullscreen .p-dialog *::after {
    border: none !important;
    border-top: none !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
  
  /* إزالة الخط من الـ header container */
  .group-members-modal-fullscreen .p-dialog-header .flex {
    border: none !important;
    border-bottom: none !important;
    box-shadow: none !important;
  }
  
  /* إزالة الخط من الـ header container في الوضع المظلم */
  .dark .group-members-modal-fullscreen .p-dialog-header .flex {
    border: none !important;
    border-bottom: none !important;
    box-shadow: none !important;
  }
`;

// Hardcoded card types to match CreateGroupForm
const HARDCODED_CARD_TYPES = [
    {
        id: 1,
        name: '600 x 400 BT 6Co',
        type_of_connection: 'bluetooth',
        setting: { width: 600, height: 400 },
        icon: <FaBluetooth size={20} />,
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    {
        id: 2,
        name: '416 x 240 NFC 4Co',
        type_of_connection: 'nfc',
        setting: { width: 416, height: 240 },
        icon: <LuNfc size={20} />,
        gradient: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)'
    },
    {
        id: 3,
        name: '300 x 400 NFC 4Co',
        type_of_connection: 'nfc',
        setting: { width: 300, height: 400 },
        icon: <LuNfc size={20} />,
        gradient: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)'
    },
    {
        id: 4,
        name: '300 x 400 NFC 3Co',
        type_of_connection: 'nfc',
        setting: { width: 300, height: 400 },
        icon: <LuNfc size={20} />,
        gradient: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)'
    }
];

// Helper function to get card type name by ID
const getCardTypeName = (cardTypeId) => {
    const cardType = HARDCODED_CARD_TYPES.find(type => type.id === cardTypeId);
    return cardType ? cardType.name : 'Unknown Card Type';
};

// Mobile FAB Styles
const mobileFabStyles = `
  .mobile-fab-container {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 1000;
  }

  .mobile-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00c3ac 0%, #02aa96 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 16px rgba(0, 195, 172, 0.4), 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: scale(1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
  }

  .mobile-fab:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 195, 172, 0.5), 0 4px 12px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #02aa96 0%, #00c3ac 100%);
  }

  .mobile-fab:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Ensure FAB stays above other elements but below burger menu */
  @media (max-width: 768px) {
    .mobile-fab-container {
      position: fixed;
      bottom: 24px;
      right: 24px;
      z-index: 1000;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = mobileFabStyles;
  if (!document.head.querySelector('style[data-mobile-fab-groups]')) {
    styleElement.setAttribute('data-mobile-fab-groups', 'true');
    document.head.appendChild(styleElement);
  }
}

/* eslint-disable react/prop-types */
function GroupsDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useGroupsDataTableContext();
    const { isMobile } = useLayout();

    const [isCreateGroupModalOpen, setisCreateGroupModalOpen] = useState(false);
    const [isEditGroupModalOpen, setIsEditGroupModalOpen] = useState(false);
    const [groupBeingEdited, setGroupBeingEdited] = useState(null); // State to hold the rowData for editing
    const deleteRow = useDeleteGroupMutation();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedType, setSelectedType] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('');
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);
    const toast = useRef(null);
    const [subscriptionError, setSubscriptionError] = useState(null);
    const [subscriptionLoading, setSubscriptionLoading] = useState(true);
    const [noPackage, setNoPackage] = useState(false);

    
    
    const cardTypes = useMemo(() => {
        if (!data) return [];
        const types = data.map(g => getCardTypeName(g.card_type_id)).filter(Boolean);
        return Array.from(new Set(types));
    }, [data]);
    const statuses = useMemo(() => {
        if (!data) return [];
        const sts = data.map(g => g.status || 'inactive').filter(Boolean);
        return Array.from(new Set(sts));
    }, [data]);

    const filteredData = useMemo(() => {
        let result = data || [];
        if (selectedType) {
            result = result.filter(g => getCardTypeName(g.card_type_id) === selectedType);
        }
        if (selectedStatus) {
            result = result.filter(g => (g.status || 'inactive') === selectedStatus);
        }
        if (searchQuery) {
            result = result.filter(g => (g.title || '').toLowerCase().includes(searchQuery.toLowerCase()));
        }
        return result;
    }, [data, selectedType, selectedStatus, searchQuery]);

    useEffect(() => {
        const checkSubscription = async () => {
            try {
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('user_id');
                if (!token || !userId) {
                    setSubscriptionError({ message: 'User not authenticated.' });
                    setSubscriptionLoading(false);
                    return;
                }
                const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                if (!response.data || Object.keys(response.data).length === 0 || response.data.error === 'No package found for this user') {
                    setNoPackage(true);
                } else {
                    setNoPackage(false);
                }
                setSubscriptionError(null);
            } catch (error) {
                if (error.response && error.response.data) {
                    const errMsg = error.response.data.error?.toLowerCase() || '';
                    if (error.response.data.error === "Your subscription has expired. Please renew your subscription to continue.") {
                        setSubscriptionError({ message: error.response.data.error });
                    } else if (
                        errMsg.includes('not found') ||
                        errMsg.includes('no package') ||
                        errMsg.includes('no active package found for this user') ||
                        errMsg.includes('must have an active package')
                    ) {
                        setNoPackage(true);
                    } else {
                        setSubscriptionError(null);
                    }
                } else {
                    setSubscriptionError(null);
                }
            } finally {
                setSubscriptionLoading(false);
            }
        };
        checkSubscription();
    }, []);

    // Add debounced search handler
    useEffect(() => {
        const timeout = setTimeout(() => {
            setLazyParams(prev => {
                const newFilters = {
                    ...prev.filters,
                    title: { value: searchQuery, matchMode: 'contains' }
                };
                
                if (JSON.stringify(prev.filters) === JSON.stringify(newFilters)) {
                    return prev;
                }
                
                return {
                    ...prev,
                    filters: newFilters
                };
            });
        }, 100);
    
        return () => clearTimeout(timeout);
    }, [searchQuery, setLazyParams]);

    useEffect(() => {
        console.log('🚀 Loading initial groups data');
        setLazyParams({ ...defaultTableConfig, ...groupsTableConfig });
    }, [setLazyParams]);

    const deleteAdHandler = async (rowData) => {
        await deleteRow.mutateAsync({
            id: rowData?.id,
        }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev, ...groupsTableConfig }));
                
                // Show success toast
                // toast.current.show({
                //     severity: 'success',
                //     summary: 'Success',
                //     detail: 'Group deleted successfully',
                //     life: 3000
                // });
            },
            onError: () => {
                // Show error toast
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to delete group',
                    life: 3000
                });
            }
        })
    }

    // --- Success Handler (called by GroupForm on successful create/update) ---
    const handleSuccess = useCallback((responseData) => {
        console.log("GroupForm reported success. Refreshing data...", responseData);
        
        // Show success message with proper delay
        const isEdit = responseData?.group?.id && groupBeingEdited?.id;
        const message = isEdit ? 'Group has been updated successfully' : 'Group has been created successfully';
        
        console.log("Attempting to show toast with message:", message);
        
        // Use setTimeout to ensure toast is ready
        setTimeout(() => {
            if (toast.current) {
                console.log("Toast ref is available, showing message");
                toast.current.show({
                    severity: 'success',
                    summary: 'Success',
                    detail: message,
                    life: 3000
                });
            } else {
                console.log("Toast ref not available, using alert as fallback");
                alert(message); // Fallback for immediate feedback
            }
        }, 200);
        
        // Refresh data by triggering the DataTable context's handler/refetch mechanism
        // Resetting page to 0 ensures user sees the newly created/updated item if on first page
        setLazyParams(prev => ({ ...prev, page: 0, ...groupsTableConfig }));
        // Optionally clear edit state, though GroupForm closing should handle this too
        setGroupBeingEdited(null);
    }, [setLazyParams, toast, groupBeingEdited]); // Depends on setLazyParams to refresh

    // --- Handler to open the Edit Modal ---
    const handleEditClick = (groupData) => {
        console.log("Editing group, data received:", groupData);
        const editedData = {
          ...groupData,
          card_type_id: groupData.card_type_id || groupData.card_type?.id 
        };
        setGroupBeingEdited(editedData);
        setIsEditGroupModalOpen(true);
      };


    const handleDeleteClick = (rowData) => {
        console.log(rowData)
        confirmDialog({
            message: 'Are you sure you want to delete this group?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => deleteAdHandler(rowData),
        });
    };

    // --- State for Designs Modal ---
    const [isDesignsModalOpen, setIsDesignsModalOpen] = useState(false);
    const queryClient = useQueryClient();
    const [designsLoading, setDesignsLoading] = useState(false);
    const [groupDesigns, setGroupDesigns] = useState([]);
    const [selectedDesigns, setSelectedDesigns] = useState([]);
    const [searchDesign, setSearchDesign] = useState("");
    const [saveLoading, setSaveLoading] = useState(false);
    const [saveMessage, setSaveMessage] = useState("");
    const [saveError, setSaveError] = useState("");
    const [currentGroupId, setCurrentGroupId] = useState(null);
    const [showImageGenerationModal, setShowImageGenerationModal] = useState(false);
    const [currentBatchId, setCurrentBatchId] = useState(null);
    const [currentDesignId, setCurrentDesignId] = useState(null);
    const [memberCards, setMemberCards] = useState([]);
    const [expandedGroups, setExpandedGroups] = useState(new Set());
    const [isMembersModalOpen, setIsMembersModalOpen] = useState(false);
    const [selectedGroupForMembers, setSelectedGroupForMembers] = useState(null);
    const [showExitWarningModal, setShowExitWarningModal] = useState(false);
    const [pendingNavigation, setPendingNavigation] = useState(null);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

    // دالة لإدارة فتح وإغلاق قسم البطاقات
    const toggleCardsSection = (groupId) => {
        setExpandedGroups(prev => {
            const newSet = new Set(prev);
            if (newSet.has(groupId)) {
                newSet.delete(groupId);
            } else {
                newSet.add(groupId);
            }
            return newSet;
        });
    };

    // دالة لفتح dialog الأعضاء
    const openMembersModal = (group) => {
        setSelectedGroupForMembers(group);
        setIsMembersModalOpen(true);
    };

    // دالة لإغلاق dialog الأعضاء
    const closeMembersModal = () => {
        setIsMembersModalOpen(false);
        setSelectedGroupForMembers(null);
    };

    // Handle exit warning modal actions
    const handleConfirmExit = () => {
        setShowExitWarningModal(false);
        if (pendingNavigation) {
            pendingNavigation();
        }
        closeMembersModal();
    };

    const handleCancelExit = () => {
        setShowExitWarningModal(false);
        setPendingNavigation(null);
    };

    const handleSaveAndExit = () => {
        setShowExitWarningModal(false);
        // Here you can add logic to save changes if needed
        closeMembersModal();
    };

    // Fetch designs and filter by group card_type_id (like GroupFilter)
    const fetchGroupDesigns = async (group) => {
        setDesignsLoading(true);
        let allDesigns = queryClient.getQueryData("getDesigns");
        try {
            if (!allDesigns || !Array.isArray(allDesigns) || allDesigns.length === 0) {
                const response = await axiosInstance.get('/designs');
                if (response.data && Array.isArray(response.data.data)) {
                    allDesigns = response.data.data;
                    queryClient.setQueryData("getDesigns", allDesigns);
                } else {
                    allDesigns = [];
                }
            }
            // filter by group card_type_id
            let filtered = allDesigns;
            if (group.card_type_id) {
                filtered = allDesigns.filter(design => {
                    if (design.card_type_id) return design.card_type_id === group.card_type_id;
                    if (design.card_type && design.card_type.id) return design.card_type.id === group.card_type_id;
                    return true;
                });
            }
            setGroupDesigns(filtered);
            return filtered;
        } catch (e) {
            setGroupDesigns([]);
            return [];
        } finally {
            setDesignsLoading(false);
        }
    };

    const openDesignsModal = async (group) => {
        console.log('openDesignsModal called', group);
        setIsDesignsModalOpen(true);
        setSelectedDesigns([]);
        setCurrentGroupId(group.id);
        const filteredDesigns = await fetchGroupDesigns(group);
        try {
            const response = await axiosInstance.get(`/groups-designs?group_id=${group.id}`);
            const apiData = Array.isArray(response.data.data) ? response.data.data : Array.isArray(response.data) ? response.data : [];
            if (apiData.length > 0) {
                const selectedIds = apiData.map(item => Number(item.design_id));
                const selected = filteredDesigns.filter(design => selectedIds.includes(Number(design.id)));

                setSelectedDesigns(selected);
            } else {
                setSelectedDesigns([]);
            }
        } catch (e) {
            
            setSelectedDesigns([]);
        }
    };
    const closeDesignsModal = () => {
        setIsDesignsModalOpen(false);
        setGroupDesigns([]);
        setSelectedDesigns([]);
        setCurrentGroupId(null);
        setSaveMessage("");
        setSaveError("");
        setSaveLoading(false);
    };

    const handleSaveDesigns = async () => {
        console.log('🚩 [handleSaveDesigns] بدأ حفظ التصاميم', { selectedDesigns, currentGroupId });
        if (!currentGroupId || selectedDesigns.length === 0) {
            console.log('🚩 [handleSaveDesigns] لا يوجد مجموعة أو تصاميم محددة');
            return;
        }
        setSaveLoading(true);
        setSaveMessage("");
        setSaveError("");
        try {
            await axiosInstance.post(`/groups/${currentGroupId}/designs`, {
                group_id: currentGroupId,
                design_ids: selectedDesigns.map(d => d.id)
            });

            const oldDesignsResponse = await axiosInstance.get(`/groups-designs?group_id=${currentGroupId}`);
            const oldDesignIds = (oldDesignsResponse.data.data || []).map(d => Number(d.design_id));

            const newDesigns = selectedDesigns.filter(d => !oldDesignIds.includes(Number(d.id)));
            console.log('🚩 [handleSaveDesigns] التصاميم الجديدة فقط:', newDesigns);
            if (newDesigns.length === 0) {
                setSaveMessage("No new designs selected.");
                return;
            }
            

            const usersResponse = await axiosInstance.get(`/groups/${currentGroupId}/users-with-cards`);
            const users = usersResponse.data.data || [];
            
            if (users.length === 0) {
                setSaveMessage("No users with cards found in this group.");
                setTimeout(() => {
                    closeDesignsModal();
                }, 2000);
                return;
            }
            
            const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const batchData = {
                total_users: users.length,
                total_designs: newDesigns.length,
                total_expected_images: users.length * newDesigns.length,
                completed_images: 0,
                status: 'processing',
                user_ids: users.map(u => u.id),
                design_ids: newDesigns.map(d => d.id),
                group_id: currentGroupId,
                created_at: new Date().toISOString()
            };
            
            console.log('🚀 [handleSaveDesigns] Batch data created:', {
                batchId,
                batchData,
                usersCount: users.length,
                designsCount: newDesigns.length,
                groupId: currentGroupId
            });
            
            // Validate currentGroupId
            if (!currentGroupId) {
                console.error('❌ [handleSaveDesigns] currentGroupId is null or undefined');
                setSaveError("Failed to get group ID. Please try again.");
                return;
            }
            
            console.log('✅ [handleSaveDesigns] currentGroupId is valid:', currentGroupId);
            
            // Save to localStorage as backup
            localStorage.setItem(`batch_${batchId}`, JSON.stringify(batchData));
            
            // Save to cache via API
            try {
                const cacheResponse = await axiosInstance.post('/cache/batch-data', {
                    batch_id: batchId,
                    batch_data: batchData
                });
                
                if (cacheResponse.data && cacheResponse.data.success) {
                    console.log('✅ Batch data saved to cache successfully:', batchId);
                    
                    // Also store batch key for this design for easy lookup
                    try {
                        const batchKeyResponse = await axiosInstance.post('/cache/store-batch-key', {
                            design_id: newDesigns[0].id,
                            batch_key: `batch_${batchId}`
                        });
                        
                        if (batchKeyResponse.data && batchKeyResponse.data.success) {
                            console.log('✅ Batch key stored successfully for design:', newDesigns[0].id);
                        } else {
                            console.warn('⚠️ Failed to store batch key:', batchKeyResponse.data);
                        }
                    } catch (error) {
                        console.warn('⚠️ Failed to store batch key:', error);
                    }
                } else {
                    console.warn('⚠️ Failed to save batch data to cache:', cacheResponse.data);
                }
            } catch (error) {
                console.error('❌ Error saving batch data to cache:', error);
                // Continue anyway as we have localStorage backup
            }
            
            
            const jobPromises = [];
            for (const user of users) {
                for (const design of newDesigns) {
                    jobPromises.push(
                        axiosInstance.post('/jobs/dispatch-design-image', {
                            user_id: user.id,
                            design_id: design.id,
                            group_id: currentGroupId
                        })
                    );
                }
            }
            await Promise.all(jobPromises);
            setSaveMessage("Image generation started for new designs and users with cards only.");
            setShowImageGenerationModal(true);
            setCurrentBatchId(batchId);
            setCurrentDesignId(newDesigns[0].id);
            setCurrentGroupId(currentGroupId); // Ensure currentGroupId is set
            console.log('🚀 [handleSaveDesigns] Modal will open with:', {
                batchId,
                designId: newDesigns[0].id,
                groupId: currentGroupId
            });
            setTimeout(() => {
                closeDesignsModal();
            }, 2000);
        } catch (error) {
            setSaveError("Failed to save designs. Please try again.");
        } finally {
            setSaveLoading(false);
        }
    };

    useEffect(() => {
        if (showImageGenerationModal) {
            console.log('🚩 [Modal] showImageGenerationModal:', showImageGenerationModal, 'batchId:', currentBatchId, 'designId:', currentDesignId);
        }
    }, [showImageGenerationModal, currentBatchId, currentDesignId]);

    // Filtered designs for search
    const filteredDesigns = groupDesigns.filter(design => {
        const name = design.title || design.name || "";
        return name.toLowerCase().includes(searchDesign.toLowerCase());
    });

    // Fetch member cards
    const fetchMemberCards = useCallback(async () => {
        try {
            const token = localStorage.getItem('token');

            const config = {
                headers: {
                    Authorization: `Bearer ${token}`,
                }
            };

            const response = await axiosInstance.get(`package-cards-statistics`, config);

            if (response.data && response.data.success) {
                setMemberCards(response.data.data);
            }
        } catch (error) {
            console.error('Error fetching member cards:', error);
            setMemberCards([]);
        }
    }, []);

    useEffect(() => {
        fetchMemberCards();
    }, [fetchMemberCards]);

    // Mobile action menu component
    /**
     * @param {{ group: any, isOpen: boolean, onClose: function }} props
     */
    const MobileActionMenu = ({ group, isOpen, onClose }) => {
        if (!isOpen) return null;

        return createPortal(
            <div
                className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center mobile-action-menu"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    backdropFilter: 'blur(8px)'
                }}
                onClick={onClose}
            >
                <div
                    className="bg-white rounded-2xl p-6 m-4 w-full max-w-sm relative shadow-2xl groups-action-menu"
                    style={{
                        zIndex: 10000,
                        backgroundColor: '#ffffff',
                        boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
                        border: '1px solid rgba(0, 0, 0, 0.05)'
                    }}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex items-center mb-6 border-b border-gray-100 pb-4">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-4 shadow-lg">
                            <span className="text-white font-bold text-lg">{group.title?.charAt(0)?.toUpperCase()}</span>
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-lg text-gray-900 truncate">{group.title}</h3>
                            <p className="text-sm text-gray-500">{getCardTypeName(group.card_type_id)}</p>
                        </div>
                    </div>

                    <div className="space-y-3">
                        {/* View Members */}
                        <Link to={`/members/group?group-id=${group.id}`}>
                            <button
                                className="w-full flex items-center p-4 text-left bg-white hover:bg-blue-50 rounded-xl border border-gray-200 transition-all duration-200 active:scale-[0.98] shadow-sm hover:shadow-md"
                                onClick={onClose}
                            >
                                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                    <FaRegEye className="text-blue-600" size={18} />
                                </div>
                                <div>
                                    <span className="font-medium text-gray-900">View Members</span>
                                    <p className="text-sm text-gray-500">See all group members</p>
                                </div>
                            </button>
                        </Link>

                        {/* Edit */}
                        <button
                            className="w-full flex items-center p-4 text-left bg-white hover:bg-green-50 rounded-xl border border-gray-200 transition-all duration-200 active:scale-[0.98] shadow-sm hover:shadow-md"
                            onClick={() => {
                                handleEditClick(group);
                                onClose();
                            }}
                        >
                            <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-4">
                                <FiEdit className="text-green-600" size={18} />
                            </div>
                            <div>
                                <span className="font-medium text-gray-900">Edit Group</span>
                                <p className="text-sm text-gray-500">Modify group details</p>
                            </div>
                        </button>

                        {/* Manage Designs */}
                        <button
                            className="w-full flex items-center p-4 text-left bg-white hover:bg-purple-50 rounded-xl border border-gray-200 transition-all duration-200 active:scale-[0.98] shadow-sm hover:shadow-md"
                            onClick={() => {
                                openDesignsModal(group);
                                onClose();
                            }}
                        >
                            <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-4">
                                <FaPalette className="text-purple-600" size={18} />
                            </div>
                            <div>
                                <span className="font-medium text-gray-900">Manage Designs</span>
                                <p className="text-sm text-gray-500">Assign designs to group</p>
                            </div>
                        </button>

                        {/* Delete */}
                        <button
                            className="w-full flex items-center p-4 text-left bg-white hover:bg-red-50 rounded-xl border border-gray-200 transition-all duration-200 active:scale-[0.98] shadow-sm hover:shadow-md"
                            onClick={() => {
                                handleDeleteClick(group);
                                onClose();
                            }}
                        >
                            <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-4">
                                <TfiTrash className="text-red-600" size={18} />
                            </div>
                            <div>
                                <span className="font-medium text-red-600">Delete Group</span>
                                <p className="text-sm text-red-400">Remove group permanently</p>
                            </div>
                        </button>
                    </div>

                    <button
                        className="w-full mt-6 p-4 bg-gray-100 hover:bg-gray-200 rounded-xl text-center font-medium transition-all duration-200 active:scale-[0.98] text-gray-700"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                </div>
            </div>,
            document.body
        );
    };

   

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-3 px-2">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm animate-pulse groups-mobile-card">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-16 h-16 bg-gray-300 rounded-full mr-3 flex-shrink-0"></div>
                                    <div className="flex-1 min-w-0">
                                        <div className="h-5 bg-gray-300 rounded-lg w-3/4 mb-2"></div>
                                        <div className="h-4 bg-gray-300 rounded-lg w-1/2 mb-2"></div>
                                        <div className="flex gap-2 mb-2">
                                            <div className="h-6 bg-gray-300 rounded-full w-20"></div>
                                            <div className="h-6 bg-gray-300 rounded-lg w-16"></div>
                                        </div>
                                        <div className="flex gap-2">
                                            <div className="h-8 bg-gray-300 rounded-full w-24"></div>
                                            <div className="h-8 bg-gray-300 rounded-full w-20"></div>
                                        </div>
                                    </div>
                                </div>
                                <div className="w-10 h-10 bg-gray-300 rounded-full flex-shrink-0"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No groups found</p>
                </div>
            );
        }

        return (
            <div className="space-y-3 px-2">
                {data.map((group) => {
                    const status = group.status || "inactive";
                    const backgroundColor = status === "active" ? "#22C55E" : status === "inactive" ? "#dc2626" : "#9ca3af";

                    return (
                    <div key={group.id} className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-200 active:scale-[0.98] groups-mobile-card">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center flex-1 min-w-0">
                                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-3 text-white text-xl font-bold shadow-lg relative overflow-hidden flex-shrink-0">
                                    <span className="z-10">{group.title?.charAt(0)?.toUpperCase()}</span>
                                    <span className="absolute inset-0 flex items-center justify-center opacity-20 z-0">
                                        <FaUsers size={48} color="#fff" />
                                    </span>
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h3 className="font-semibold text-lg text-gray-900 mb-1 truncate">{group.title}</h3>
                                    <p className="text-sm text-gray-500 mb-2 line-clamp-2">{group.description}</p>
                                    <div className="flex items-center gap-2 mb-2 flex-wrap">
                                        <span className="inline-block bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-xs font-bold border border-blue-300">{getCardTypeName(group.card_type_id)}</span>
                                        <span className="inline-block text-white rounded-lg font-bold text-xs py-1 px-2 capitalize shadow-sm" style={{ backgroundColor }}>{status}</span>
                                    </div>
                                        {/* User Count Badge - Mobile View */}
                                        <div className="flex items-center gap-2 mt-2">
                                            <div className="flex items-center gap-1 bg-blue-50 border border-blue-200 rounded-full px-2 py-1 group relative cursor-help">
                                                <svg className="w-3.5 h-3.5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M4.5 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM14.25 8.625a3.375 3.375 0 116.75 0 3.375 3.375 0 01-6.75 0zM1.5 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM17.25 19.128l-.001.144a2.25 2.25 0 01-.233.96 10.088 10.088 0 005.06-1.01.75.75 0 00.42-.643 4.875 4.875 0 00-6.957-4.611 8.586 8.586 0 011.71 5.157v.003z"/>
                                                </svg>
                                                <span className="text-blue-700 font-bold text-xs">
                                                    {typeof (group.users?.length || group.users_count) === 'number' ? (group.users?.length || group.users_count) : 0}
                                                </span>
                                                <span className="text-blue-600 text-xs ml-1">Members</span>
                                                {/* Tooltip */}
                                                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                                    Total Members
                                                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                                </div>
                                            </div>
                                            <div className="relative mt-3 group cursor-help">
                                                <div className="relative inline-flex items-center justify-center">
                                                    <FaIdCardAlt  className="w-6 h-6 text-green-600" />
                                                    <span className="absolute inset-0 flex items-center justify-center text-white font-bold text-xs bg-green-600 rounded-sm" style={{ fontSize: '8px', width: '16px', height: '10px', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}>
                                                        {typeof group.users_with_cards_count === 'number' ? group.users_with_cards_count : 0}
                                                    </span>
                                                </div>
                                                {/* Tooltip */}
                                                <div className="absolute bottom-full right-0 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                                    Assigned Members
                                                    <div className="absolute top-full right-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                                </div>
                                            </div>
                                        </div>
                                </div>
                            </div>
                            <button
                                className="p-3 hover:bg-gray-100 rounded-full transition-colors active:scale-95"
                                onClick={() => setMobileActionMenuOpen(group.id)}
                            >
                                <HiDotsVertical className="text-gray-500" size={24} />
                            </button>
                        </div>
                    </div>
                    );
                })}

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        group={data.find(g => g.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };

    if (subscriptionLoading) {
        return <p className="text-center">Loading...</p>;
    }
    if (subscriptionError && subscriptionError.message === "Your subscription has expired. Please renew your subscription to continue.") {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        <motion.div
                            className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
                            style={{
                                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
                            }}
                            animate={{
                                background: [
                                    'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                                    'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                                    'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
                                ],
                                rotate: -360,
                            }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear",
                            }}
                        />
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">Subscription Status</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Subscription Expired
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            {subscriptionError.message}
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Please renew your subscription to continue using our services.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-red-50 to-white"
                            style={{ borderTop: '6px solid #ef4444' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">Expired Plan</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-red-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-red-600 font-medium">Your subscription has expired</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-red-100 text-red-800 flex items-center justify-center">
                                <span className="font-medium">⚠️ Please renew your subscription to continue</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => window.location.href = `${import.meta.env.VITE_ENV}/manager/Packages`}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    Renew Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }
    if (noPackage) {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        <motion.div
                            className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
                            style={{
                                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
                            }}
                            animate={{
                                background: [
                                    'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                                    'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                                    'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
                                ],
                                rotate: -360,
                            }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear",
                            }}
                        />
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">No Package Found</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Unlock All Features With a Package
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            We couldn&apos;t find any active package for your account. To access all features and design templates, please purchase a package.
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Click the button below to explore available packages and unlock the full potential of your dashboard.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-blue-50 to-white"
                            style={{ borderTop: '6px solid #3b82f6' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">No Active Package</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-blue-600 font-medium">No package is currently assigned to your account</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-blue-100 text-blue-800 flex items-center justify-center">
                                <span className="font-medium">💡 Purchase a package to unlock all features</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => window.location.href = `${import.meta.env.VITE_ENV}/manager/Packages`}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    View Packages
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }

    return (
        <React.Fragment>
        {/* CSS للـ dialog ملء الشاشة */}
        <style>{fullscreenMembersModalStyles}</style>
        
        <div className="w-full min-w-full h-full flex flex-col">
            <Toast ref={toast} position="top-right" />     

                <ConfirmDialog
                group="headless"
                content={(options) => (
                    <div className="flex flex-col items-center p-5">
                        <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
                        <span className="text-xl font-bold mb-2">{options.message}</span>
                        <div className="flex gap-3">
                            <button className="p-button p-component" onClick={options.accept}>
                                Yes
                            </button>
                            <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                                No
                            </button>
                        </div>
                    </div>
                )}
            />

                {/* Professional Header Section */}
                <div className="w-full mb-8">
                <div className="w-full">
                    {/* Header Background with Light Gradient */}
                    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 shadow-xl border border-gray-200 groups-header-container">
                        {/* Animated Background Pattern */}
                        <div className="absolute inset-0 opacity-5">
                            <div className="absolute inset-0" style={{
                                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                                backgroundSize: '60px 60px'
                            }}></div>
                        </div>
                        
                        {/* Floating Elements */}
                        <div className="absolute top-4 right-4 w-20 h-20 bg-blue-200/30 rounded-full blur-xl"></div>
                        <div className="absolute bottom-4 left-4 w-16 h-16 bg-purple-200/30 rounded-full blur-xl"></div>
                            
                            {/* Header Content */}
                             <div className="relative z-10 p-4">
                                {/* Title Section - Improved Mobile Layout */}
                             <div className={`${isMobile ? 'flex flex-col space-y-3 mb-4' : 'flex flex-row items-center justify-between mb-4'}`}>
                                 <div className={`${isMobile ? 'w-full' : 'mb-0'}`}>
                                     <div className={`flex items-center gap-3 ${isMobile ? 'justify-center' : ''}`}>
                                        <div className="relative">
                                            <div className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg`}>
                                                 <FaUsers className={`text-white ${isMobile ? 'text-base' : 'text-lg'}`} />
                                        </div>
                                             <div className={`absolute -top-1 -right-1 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'} bg-green-500 rounded-full flex items-center justify-center border-2 border-white shadow-md`}>
                                                <span className={`text-white ${isMobile ? 'text-xs' : 'text-xs'} font-bold`}>
                                                    {filteredData?.length || 0}
                                                </span>
                                            </div>
                                        </div>
                                        <div className={`${isMobile ? 'text-center' : ''}`}>
                                             <h1 className={`${isMobile ? 'text-lg' : 'text-xl lg:text-2xl'} font-bold text-gray-900 mb-0.5`}>
                                            Groups Management
                                        </h1>
                                             <p className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                                                 {isMobile ? 'Manage your groups' : 'Organize and manage your team groups efficiently'}
                                    </p>
                                         </div>
                                    </div>
                                </div>

                                {/* Desktop Create Button */}
                                {!isMobile && (
                                    <button
                                        onClick={() => setisCreateGroupModalOpen(true)}
                                        className="group relative px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-emerald-400/30"
                                    >
                                        <div className="relative">
                                            <FaPlus className="text-base group-hover:rotate-90 transition-transform duration-300" />
                                            </div>
                                        <span className="text-base">Create New Group</span>
                                        <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-teal-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    </button>
                                )}
                            </div>

                                                         {/* Search and Filters Section - Improved Mobile Layout */}
                             <div className={`${isMobile ? 'space-y-3' : 'grid grid-cols-1 lg:grid-cols-12 gap-3 items-end'}`}>
                                {/* Search Input - Mobile Optimized */}
                                <div className={`relative ${isMobile ? 'w-full order-1' : 'lg:col-span-5'}`}>
                                    <div className="relative">
                                        <div className={`absolute inset-y-0 left-0 ${isMobile ? 'pl-3' : 'pl-4'} flex items-center pointer-events-none z-10`}>
                                            <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 rounded-xl flex items-center justify-center shadow-lg border border-slate-500/30 group-hover:scale-110 transition-all duration-300 hover:shadow-xl hover:from-slate-600 hover:via-slate-500 hover:to-slate-700`}>
                                                <svg className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-white group-hover:scale-110 transition-transform duration-300 group-hover:rotate-12`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                                </svg>
                                                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                            </div>
                                            </div>
                                            <input
                                                type="text"
                                             placeholder={isMobile ? "Search groups..." : "Search groups by title, description..."}
                                             className={`w-full ${isMobile ? 'pl-12 pr-3 py-2' : 'pl-16 pr-4 py-2.5'} bg-white/90 backdrop-blur-sm border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 ${isMobile ? 'text-sm' : 'text-sm'} shadow-sm group`}
                                                value={searchQuery}
                                                onChange={(e) => setSearchQuery(e.target.value)}
                                            />
                                        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-xl opacity-0 focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                                        </div>
                                    </div>

                                {/* Filter Dropdowns - Mobile Optimized */}
                                <div className={`${isMobile ? 'grid grid-cols-2 gap-2 order-2' : 'lg:col-span-4 grid grid-cols-2 gap-4'}`}>
                                    {/* Type Filter */}
                                    <div className="relative">
                                        <label className={`block text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'} font-medium ${isMobile ? 'mb-1' : 'mb-2'} flex items-center gap-1`}>
                                            <div className="relative group">
                                                <MdCategory className={`text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 18} />
                                                <div className="absolute inset-0 bg-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                                            </div>
                                            {isMobile ? 'Type' : 'Card Type'}
                                        </label>
                                        <div className="relative">
                                            <select
                                                 className={`w-full ${isMobile ? 'px-3 py-1.5 pl-8 text-xs' : 'px-4 py-2 pl-12 text-sm'} bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 appearance-none cursor-pointer shadow-sm`}
                                                value={selectedType}
                                                onChange={e => setSelectedType(e.target.value)}
                                            >
                                                <option value="" className="bg-white text-gray-900">All Types</option>
                                                {cardTypes.map(type => (
                                                    <option key={type} value={type} className="bg-white text-gray-900">{type}</option>
                                                ))}
                                            </select>
                                            <div className={`absolute inset-y-0 left-0 ${isMobile ? 'pl-2' : 'pl-4'} flex items-center pointer-events-none`}>
                                                <div className="relative group">
                                                    <BsCardText className={`text-blue-500 group-hover:text-blue-600 transition-colors duration-300 group-hover:scale-110 group-hover:-rotate-6`} size={isMobile ? 12 : 16} />
                                                    <div className="absolute inset-0 bg-blue-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                                </div>
                                            </div>
                                            <div className={`absolute inset-y-0 right-0 ${isMobile ? 'pr-2' : 'pr-3'} flex items-center pointer-events-none`}>
                                                <svg className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} text-gray-500`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Status Filter */}
                                    <div className="relative">
                                        <label className={`block text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'} font-medium ${isMobile ? 'mb-1' : 'mb-2'} flex items-center gap-1`}>
                                            <div className="relative group">
                                                <MdFilterList className={`text-purple-600 group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-110 group-hover:-rotate-6`} size={isMobile ? 14 : 18} />
                                                <div className="absolute inset-0 bg-purple-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                                            </div>
                                            Status
                                        </label>
                                        <div className="relative">
                                            <select
                                                 className={`w-full ${isMobile ? 'px-3 py-1.5 pl-8 text-xs' : 'px-4 py-2 pl-12 text-sm'} bg-white/80 backdrop-blur-sm border border-gray-200 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 appearance-none cursor-pointer shadow-sm`}
                                                value={selectedStatus}
                                                onChange={e => setSelectedStatus(e.target.value)}
                                            >
                                                <option value="" className="bg-white text-gray-900">All Statuses</option>
                                                {statuses.map(status => (
                                                    <option key={status} value={status} className="bg-white text-gray-900">{status}</option>
                                                ))}
                                            </select>
                                            <div className={`absolute inset-y-0 left-0 ${isMobile ? 'pl-2' : 'pl-4'} flex items-center pointer-events-none`}>
                                                <div className="relative group">
                                                    <BiGroup className={`text-purple-500 group-hover:text-purple-600 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 12 : 16} />
                                                    <div className="absolute inset-0 bg-purple-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                                </div>
                                            </div>
                                            <div className={`absolute inset-y-0 right-0 ${isMobile ? 'pr-2' : 'pr-3'} flex items-center pointer-events-none`}>
                                                <svg className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} text-gray-500`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Stats Section - Mobile Optimized */}
                                <div className={`${isMobile ? 'order-3 mt-2' : 'lg:col-span-3'}`}>
                                    <div className={`grid grid-cols-3 ${isMobile ? 'gap-2' : 'gap-3'}`}>
                                        {/* Total Groups */}
                                        <div className={`bg-white/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                                        <div className="text-center">
                                                <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                                                    <div className="relative group">
                                                        <BiGroup className={`text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                                                        <div className="absolute inset-0 bg-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                            </div>
                                                    <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-900 group-hover:text-blue-700 transition-colors duration-300`}>
                                                {filteredData?.length || 0}
                                            </div>
                                        </div>
                                                <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 group-hover:text-blue-600 transition-colors duration-300`}>
                                                    {isMobile ? 'Total' : 'Groups'}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Active Groups */}
                                        <div className={`bg-white/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                                        <div className="text-center">
                                                <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                                                    <div className="relative group">
                                                        <BsCheckCircle className={`text-green-600 group-hover:text-green-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                                                        <div className="absolute inset-0 bg-green-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                            </div>
                                                    <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-green-600 group-hover:text-green-700 transition-colors duration-300`}>
                                                {filteredData?.filter(g => g.status === 'active')?.length || 0}
                                            </div>
                                        </div>
                                                <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 group-hover:text-green-600 transition-colors duration-300`}>Active</div>
                                            </div>
                                        </div>

                                        {/* Inactive Groups */}
                                        <div className={`bg-white/80 backdrop-blur-sm rounded-xl ${isMobile ? 'p-1.5' : 'p-2'} border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 group`}>
                                        <div className="text-center">
                                                <div className={`flex items-center justify-center ${isMobile ? 'gap-1 mb-0.5' : 'gap-2 mb-1'}`}>
                                                    <div className="relative group">
                                                        <BsXCircle className={`text-red-600 group-hover:text-red-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6`} size={isMobile ? 14 : 20} />
                                                        <div className="absolute inset-0 bg-red-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                            </div>
                                                    <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-red-600 group-hover:text-red-700 transition-colors duration-300`}>
                                                        {filteredData?.filter(g => g.status === 'inactive')?.length || 0}
                                            </div>
                                        </div>
                                                <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 group-hover:text-red-600 transition-colors duration-300`}>Inactive</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                            {/* Quick Actions */}
                            <div className="mt-6 flex flex-wrap gap-3">
                                <button 
                                    onClick={() => {
                                        setSearchQuery('');
                                        setSelectedType('');
                                        setSelectedStatus('');
                                    }}
                                    className="px-4 py-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-lg text-gray-700 hover:bg-white hover:border-gray-300 transition-all duration-300 text-sm font-medium shadow-sm flex items-center gap-2 group"
                                >
                                    <div className="relative">
                                        <MdClear className="text-gray-600 group-hover:text-gray-800 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-12" size={16} />
                                        <div className="absolute inset-0 bg-gray-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                </div>
                                    Clear Filters
                                </button>
                                <div className="px-4 py-2 bg-blue-100/80 backdrop-blur-sm border border-blue-200 rounded-lg text-blue-700 text-sm font-medium shadow-sm flex items-center gap-2 group">
                                    <div className="relative">
                                        <MdTrendingUp className="text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:-rotate-12" size={16} />
                                        <div className="absolute inset-0 bg-blue-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                                    </div>
                                    Showing {filteredData?.length || 0} of {data?.length || 0} groups
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Container for Table and Modals */}

                    {/* --- Render Create Group Modal --- */}
                    {isCreateGroupModalOpen && (
                        <GroupForm
                            isModalOpen={isCreateGroupModalOpen}
                        setIsModalOpen={setisCreateGroupModalOpen}
                        onSuccess={handleSuccess}
                        />
                    )}

                    {/* --- Render Edit Group Modal --- */}
                    {isEditGroupModalOpen && groupBeingEdited && (
                        <GroupForm
                            isModalOpen={isEditGroupModalOpen}
                        setIsModalOpen={setIsEditGroupModalOpen}
                        onSuccess={handleSuccess}
                        groupToEdit={groupBeingEdited}
                        />
                    )}

                {/* Conditional rendering for mobile vs desktop */}
                <div className="flex-grow h-full">
                    {isMobile ? (
                        <MobileListView />
                    ) : (
                        <>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-8">
                            {filteredData && filteredData.length > 0 ? filteredData.map((group) => {
                                const status = group.status || "inactive";
                                const backgroundColor = status === "active" ? "#22C55E" : status === "inactive" ? "#dc2626" : "#9ca3af";
                                
                                return (
                                    <div key={group.id} className="bg-white rounded-2xl shadow-lg p-8 flex flex-col border border-gray-200 hover:shadow-2xl transition relative groups-card">
                                            {/* User Count Badge - Top Right Corner */}
                                            <div className="absolute top-4 right-4 flex flex-col items-end gap-1">
                                                {/* Total Users Count */}
                                                <div className="flex items-center gap-1 bg-blue-50 border border-blue-200 rounded-full px-3 py-1 group relative cursor-help">
                                                    <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M4.5 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM14.25 8.625a3.375 3.375 0 116.75 0 3.375 3.375 0 01-6.75 0zM1.5 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM17.25 19.128l-.001.144a2.25 2.25 0 01-.233.96 10.088 10.088 0 005.06-1.01.75.75 0 00.42-.643 4.875 4.875 0 00-6.957-4.611 8.586 8.586 0 011.71 5.157v.003z"/>
                                                </svg>
                                                <span className="text-blue-700 font-bold text-xs">
                                                    {typeof (group.users?.length || group.users_count) === 'number' ? (group.users?.length || group.users_count) : 0}
                                                </span>
                                                <span className="text-blue-600 text-xs ml-1">Members</span>
                                                {/* Tooltip */}
                                                <div className="absolute bottom-full right-0 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                                    Total Members
                                                    <div className="absolute top-full right-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                                </div>
                                            </div>
                                                {/* Users with Cards Count */}
                                                <div className="flex items-center gap-1 bg-green-50 border border-green-200 rounded-full px-3 py-1 group relative cursor-help mt-3">
                                                    <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M4.5 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM14.25 8.625a3.375 3.375 0 116.75 0 3.375 3.375 0 01-6.75 0zM1.5 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM17.25 19.128l-.001.144a2.25 2.25 0 01-.233.96 10.088 10.088 0 005.06-1.01.75.75 0 00.42-.643 4.875 4.875 0 00-6.957-4.611 8.586 8.586 0 011.71 5.157v.003z"/>
                                                        <path d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 9a.75.75 0 00-1.5 0v2.25H9a.75.75 0 000 1.5h2.25V15a.75.75 0 001.5 0v-2.25H15a.75.75 0 000-1.5h-2.25V9z" fillOpacity="0.3"/>
                                                    </svg>
                                                    <span className="text-green-700 font-bold text-xs">
                                                        {typeof group.users_with_cards_count === 'number' ? group.users_with_cards_count : 0}
                                                    </span>
                                                    <span className="text-green-600 text-xs ml-1">With Cards</span>
                                                    {/* Tooltip */}
                                                    <div className="absolute bottom-full right-0 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                                                          Assigned Members
                                                        <div className="absolute top-full right-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                        <div className="flex items-center mb-4 relative">
                                            <div className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center mr-3 text-white text-3xl font-bold shadow relative overflow-hidden">
                                                <span className="z-10">{group.title?.charAt(0)?.toUpperCase()}</span>
                                                <span className="absolute inset-0 flex items-center justify-center opacity-20 z-0">
                                                    <FaUsers size={64} color="#fff" />
                                                </span>
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="font-semibold text-lg text-gray-900 mb-1">{group.title}</h3>
                                                <p className="text-sm text-gray-500 mb-1">{group.description}</p>
                                                <div className="flex items-center gap-2 mb-1">
                                                    <span className="inline-block bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-xs font-bold border border-blue-300">{getCardTypeName(group.card_type_id)}</span>
                                                    <span className="inline-block text-white rounded-[6px] font-bold text-xs py-1 px-2 capitalize" style={{ backgroundColor }}>{status}</span>
                                                </div>
                                                {/* Avatar Group for group members */}
                                                {group.users && group.users.length > 0 && (
                                                  <div className="mt-4">
                                                    <div className="flex -space-x-3">
                                                      {group.users.slice(0, 5).map((member, idx) => {
                                                        const imageSrc = member.image || member.avatar || member.photo || member.profile_image || '';
                                                        const memberName = member.name || member.full_name || member.email || '?';
                                                        
                                                        return (
                                                          <div
                                                            key={idx}
                                                            className="relative group"
                                                            style={{ zIndex: 10 + idx }}
                                                          >
                                                            <Avatar
                                                              className="w-10 h-10 border-2 border-white bg-gray-100 shadow relative cursor-pointer transition-all duration-300 ease-out group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-blue-200 group-hover:border-blue-300 hover:z-20 group-hover:-translate-y-1"
                                                            >
                                                              <AvatarImage
                                                                src={imageSrc}
                                                                alt={memberName}
                                                                className="object-cover w-full h-full transition-all duration-300 group-hover:brightness-110 group-hover:contrast-110"
                                                              />
                                                              <AvatarFallback className="bg-blue-200 text-blue-900 font-bold transition-all duration-300 group-hover:bg-blue-300 group-hover:text-blue-800">
                                                                {memberName.charAt(0).toUpperCase()}
                                                              </AvatarFallback>
                                                            </Avatar>
                                                            {/* Custom Tooltip with enhanced animation */}
                                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gradient-to-r from-gray-800 to-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out group-hover:mb-2 scale-95 group-hover:scale-100 whitespace-nowrap z-50 shadow-xl border border-gray-600 group-hover:-translate-y-1">
                                                              <div className="font-medium">{memberName}</div>
                                                              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-700"></div>
                                                              {/* Glow effect */}
                                                              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
                                                            </div>
                                                            {/* Hover ring effect */}
                                                            <div className="absolute inset-0 rounded-full border-2 border-transparent group-hover:border-blue-400 transition-all duration-300 ease-out scale-110 opacity-0 group-hover:opacity-100 group-hover:scale-125 group-hover:-translate-y-1"></div>
                                                          </div>
                                                        );
                                                      })}
                                                      {/* Show remaining count if more than 5 members */}
                                                      {group.users.length > 5 && (
                                                        <div className="relative group">
                                                          <div className="w-10 h-10 border-2 border-white bg-gradient-to-br from-gray-400 to-gray-500 rounded-full shadow relative flex items-center justify-center cursor-pointer transition-all duration-300 ease-out group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-gray-300 group-hover:border-gray-300 hover:z-20 group-hover:bg-gradient-to-br group-hover:from-gray-500 group-hover:to-gray-600 group-hover:-translate-y-1" style={{ zIndex: 15 }}>
                                                            <span className="text-white font-bold text-xs transition-all duration-300 group-hover:scale-110">
                                                              +{group.users.length - 5}
                                                            </span>
                                                          </div>
                                                          {/* Custom Tooltip for remaining count with enhanced animation */}
                                                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gradient-to-r from-gray-800 to-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out group-hover:mb-2 scale-95 group-hover:scale-100 whitespace-nowrap z-50 shadow-xl border border-gray-600 group-hover:-translate-y-1">
                                                            <div className="font-medium">{group.users.length - 5} more members</div>
                                                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-700"></div>
                                                            {/* Glow effect */}
                                                            <div className="absolute inset-0 bg-gradient-to-r from-gray-400 to-gray-500 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
                                                          </div>
                                                          {/* Hover ring effect */}
                                                          <div className="absolute inset-0 rounded-full border-2 border-transparent group-hover:border-gray-400 transition-all duration-300 ease-out scale-110 opacity-0 group-hover:opacity-100 group-hover:scale-125 group-hover:-translate-y-1"></div>
                                                        </div>
                                                      )}
                                                    </div>
                                                  </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-2 gap-2 mt-auto">
                                            <button 
                                                onClick={() => openMembersModal(group)}
                                                className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg font-medium transition text-xs"
                                            >
                                                <FaRegEye size={14} />
                                                <span>Members</span>
                                            </button>
                                            <button className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-green-50 hover:bg-green-100 text-green-700 rounded-lg font-medium transition text-xs" onClick={() => handleEditClick(group)}>
                                                <FiEdit size={14} />
                                                <span>Edit</span>
                                            </button>
                                            <button className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg font-medium transition text-xs" onClick={() => handleDeleteClick(group)}>
                                                <TfiTrash size={14} />
                                                <span>Delete</span>
                                            </button>
                                            <button className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-purple-50 hover:bg-purple-100 text-purple-700 rounded-lg font-medium transition text-xs" onClick={() => openDesignsModal(group)}>
                                                <FaPalette size={14} />
                                                <span>Designs</span>
                                            </button>
                                        </div>

                                        {/* Cards Section for each group */}
                                        {memberCards && memberCards.cards_by_type && (
                                            <>
                                                <div className="mt-6 border-t border-gray-200 pt-6">
                                                    <div className="flex items-center justify-between mb-4">
                                                    <div className="flex items-center gap-3">
                                                        <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
                                                            <FaCreditCard className="text-white" size={16} />
                                                        </div>
                                                        <div>
                                                            <h4 className="text-sm font-bold text-gray-800">Group Cards Management</h4>
                                                            <p className="text-xs text-gray-500">View and manage card assignments</p>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-3">
                                                        {/* Toggle Button */}
                                                        <button
                                                            onClick={() => toggleCardsSection(group.id)}
                                                            className="group relative p-2 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50 rounded-xl border border-gray-200 hover:border-blue-300 transition-all duration-300 shadow-sm hover:shadow-md"
                                                            title={expandedGroups.has(group.id) ? "Hide cards" : "Show cards"}
                                                        >
                                                            <svg 
                                                                className={`w-4 h-4 text-gray-600 group-hover:text-blue-600 transition-all duration-300 ${expandedGroups.has(group.id) ? 'rotate-180' : ''}`} 
                                                                fill="none" 
                                                                stroke="currentColor" 
                                                                viewBox="0 0 24 24"
                                                            >
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                                            </svg>
                                                            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                                                        </button>
                                                    </div>
                                                </div>

                                                {/* Cards Content - Show only if expanded */}
                                                <motion.div
                                                    initial={false}
                                                    animate={expandedGroups.has(group.id) ? { height: "auto", opacity: 1 } : { height: 0, opacity: 0 }}
                                                    transition={{ duration: 0.3, ease: "easeInOut" }}
                                                    className="overflow-hidden"
                                                >
                                                    <div className="space-y-4">
                                                        {/* Find cards for this group's card type */}
                                                        {(() => {
                                                            const groupCardType = memberCards.cards_by_type.find(
                                                                cardType => cardType.type_id === group.card_type_id
                                                            );

                                                            if (!groupCardType) {
                                                                return (
                                                                    <motion.div 
                                                                        className="text-center py-8 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border-2 border-dashed border-gray-300"
                                                                        initial={{ opacity: 0, scale: 0.95 }}
                                                                        animate={{ opacity: 1, scale: 1 }}
                                                                        transition={{ duration: 0.3 }}
                                                                    >
                                                                        <div className="p-3 bg-gray-200 rounded-full w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                                                                            <FaCreditCard className="text-gray-500 text-xl" />
                                                                        </div>
                                                                        <p className="text-gray-600 font-medium">No cards found for this group type</p>
                                                                        <p className="text-xs text-gray-500 mt-1">This group doesn&apos;t have any associated cards</p>
                                                                    </motion.div>
                                                                );
                                                            }

                                                            const availableCards = groupCardType.cards_available || [];
                                                            const assignedCards = groupCardType.cards_assigns || [];
                                                            
                                                            // Filter assigned cards for this specific group
                                                            const groupAssignedCards = assignedCards.filter(
                                                                card => card.group_info && card.group_info.group_id === group.id
                                                            );

                                                            return (
                                                                <div className="space-y-4">
                                                                    {/* Available Cards */}
                                                                    {availableCards.length > 0 && (
                                                                        <motion.div 
                                                                            className="bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 rounded-xl p-4 border border-green-200 shadow-sm"
                                                                            initial={{ opacity: 0, x: -20 }}
                                                                            animate={{ opacity: 1, x: 0 }}
                                                                            transition={{ duration: 0.4, delay: 0.1 }}
                                                                        >
                                                                            <div className="flex items-center justify-between mb-3">
                                                                                <h5 className="text-sm font-bold text-green-800 flex items-center gap-2">
                                                                                    <div className="p-1.5 bg-green-500 rounded-lg">
                                                                                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                                        </svg>
                                                                                    </div>
                                                                                    Available Cards
                                                                                </h5>
                                                                                <span className="px-3 py-1 bg-green-500 text-white text-xs font-bold rounded-full shadow-sm">
                                                                                    {availableCards.length}
                                                                                </span>
                                                                            </div>
                                                                            <div className="space-y-3 max-h-40 overflow-y-auto custom-scrollbar">
                                                                                {availableCards.map((card, index) => (
                                                                                    <motion.div 
                                                                                        key={`available-${card.id || index}`} 
                                                                                        className="group relative bg-white rounded-lg p-3 border border-green-200 hover:border-green-300 hover:shadow-lg transition-all duration-200 cursor-pointer"
                                                                                        initial={{ opacity: 0, y: 10 }}
                                                                                        animate={{ opacity: 1, y: 0 }}
                                                                                        transition={{ duration: 0.3, delay: index * 0.1 }}
                                                                                        whileHover={{ scale: 1.01, y: -1 }}
                                                                                    >
                                                                                        <div className="flex items-center justify-between mb-2">
                                                                                            <div className="flex items-center gap-2">
                                                                                                <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-green-500 rounded-full shadow-sm"></div>
                                                                                                <span className="font-mono font-bold text-green-700 text-sm">
                                                                                                    {card.number}
                                                                                                </span>
                                                                                            </div>
                                                                                            <span className="text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full">
                                                                                                Available
                                                                                            </span>
                                                                                        </div>
                                                                                        <div className="flex items-center gap-2 text-xs text-gray-600">
                                                                                            <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                                            </svg>
                                                                                            <span className="text-gray-500">Ready to assign</span>
                                                                                        </div>
                                                                                        <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg opacity-0 group-hover:opacity-5 transition-opacity duration-200"></div>
                                                                                    </motion.div>
                                                                                ))}
                                                                            </div>
                                                                        </motion.div>
                                                                    )}

                                                                    {/* Assigned Cards for this group */}
                                                                    {groupAssignedCards.length > 0 && (
                                                                        <motion.div 
                                                                            className="bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm"
                                                                            initial={{ opacity: 0, x: 20 }}
                                                                            animate={{ opacity: 1, x: 0 }}
                                                                            transition={{ duration: 0.4, delay: 0.2 }}
                                                                        >
                                                                            <div className="flex items-center justify-between mb-3">
                                                                                <h5 className="text-sm font-bold text-blue-800 flex items-center gap-2">
                                                                                    <div className="p-1.5 bg-blue-500 rounded-lg">
                                                                                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                                                        </svg>
                                                                                    </div>
                                                                                    Group Assigned Cards
                                                                                </h5>
                                                                                <span className="px-3 py-1 bg-blue-500 text-white text-xs font-bold rounded-full shadow-sm">
                                                                                    {groupAssignedCards.length}
                                                                                </span>
                                                                            </div>
                                                                            <div className="space-y-3 max-h-40 overflow-y-auto custom-scrollbar">
                                                                                {groupAssignedCards.map((card, index) => (
                                                                                    <motion.div 
                                                                                        key={`assigned-${card.id || index}`} 
                                                                                        className="group relative bg-white rounded-lg p-3 border border-blue-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200"
                                                                                        initial={{ opacity: 0, y: 10 }}
                                                                                        animate={{ opacity: 1, y: 0 }}
                                                                                        transition={{ duration: 0.3, delay: index * 0.1 }}
                                                                                        whileHover={{ scale: 1.01, y: -1 }}
                                                                                    >
                                                                                        <div className="flex items-center justify-between mb-2">
                                                                                            <div className="flex items-center gap-2">
                                                                                                <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-sm"></div>
                                                                                                <span className="font-mono font-bold text-blue-700 text-sm">
                                                                                                    {card.number}
                                                                                                </span>
                                                                                            </div>
                                                                                            <span className="text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full">
                                                                                                {card.group_info?.assigned_user_name || 'Unknown'}
                                                                                            </span>
                                                                                        </div>
                                                                                        <div className="flex items-center gap-2 text-xs text-gray-600">
                                                                                            <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                                                            </svg>
                                                                                            <span className="truncate max-w-32" title={card.group_info?.assigned_user_name}>
                                                                                                {card.group_info?.assigned_user_name || 'Unknown User'}
                                                                                            </span>
                                                                                        </div>
                                                                                        <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-lg opacity-0 group-hover:opacity-5 transition-opacity duration-200"></div>
                                                                                    </motion.div>
                                                                                ))}
                                                                            </div>
                                                                        </motion.div>
                                                                    )}

                                                                    {/* No cards message */}
                                                                    {availableCards.length === 0 && groupAssignedCards.length === 0 && (
                                                                        <motion.div 
                                                                            className="text-center py-8 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border-2 border-dashed border-gray-300"
                                                                            initial={{ opacity: 0, scale: 0.95 }}
                                                                            animate={{ opacity: 1, scale: 1 }}
                                                                            transition={{ duration: 0.3 }}
                                                                        >
                                                                            <div className="p-3 bg-gray-200 rounded-full w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                                                                                <FaCreditCard className="text-gray-500 text-xl" />
                                                                            </div>
                                                                            <p className="text-gray-600 font-medium">No cards available for this group</p>
                                                                            <p className="text-xs text-gray-500 mt-1">This group doesn&apos;t have any cards assigned or available</p>
                                                                        </motion.div>
                                                                    )}
                                                                </div>
                                                            );
                                                        })()}
                                                    </div>
                                                </motion.div>
                                                </div>
                                            </>
                                        )}
                                    </div>
                                );
                            }) : null}
                        </div>
                        <div className="flex justify-center mt-12">
                            <div className="inline-flex items-center gap-2 groups-pagination">
                                <button
                                    className="px-3 py-1 rounded border border-gray-300 bg-white hover:bg-gray-100 disabled:opacity-50 text-sm"
                                    onClick={() => dataHandler({ first: Math.max(0, (lazyParams?.first || 0) - (lazyParams?.rows || 5)), rows: lazyParams?.rows })}
                                    disabled={!lazyParams || (lazyParams.first || 0) === 0}
                                >Previous</button>
                                <span className="mx-2 text-gray-600 text-sm">Page {Math.floor((lazyParams?.first || 0) / (lazyParams?.rows || 5)) + 1} of {Math.ceil((totalRecords || 0) / (lazyParams?.rows || 5))}</span>
                                <button
                                    className="px-3 py-1 rounded border border-gray-300 bg-white hover:bg-gray-100 disabled:opacity-50 text-sm"
                                    onClick={() => dataHandler({ first: (lazyParams?.first || 0) + (lazyParams?.rows || 5), rows: lazyParams?.rows })}
                                    disabled={!lazyParams || (lazyParams.first || 0) + (lazyParams.rows || 5) >= (totalRecords || 0)}
                                >Next</button>
                            </div>
                        </div>
                        </>
                    )}
                </div>
                {/* Dialog لعرض التصاميم */}
                <Dialog
                    visible={isDesignsModalOpen}
                    onHide={closeDesignsModal}
                    header="Group Designs"
                    style={isMobile ? { width: '95vw', height: '90vh' } : { width: '700px', maxWidth: '98vw' }}
                    breakpoints={{ '960px': '80vw', '641px': '95vw' }}
                    modal
                    className={`group-designs-modal ${isMobile ? 'mobile-group-dialog' : ''}`}
                    maximizable={false}
                    resizable={false}
                >
                    {designsLoading ? (
                        <div className="flex justify-center items-center py-10"><i className="pi pi-spin pi-spinner text-2xl text-blue-500"></i></div>
                    ) : groupDesigns.length === 0 ? (
                        <div className="text-center text-gray-500 dark:text-gray-300 py-8">No designs found for this group.</div>
                    ) : (
                        <div>
                            {/* Search Field */}
                            <div className="mb-4 flex items-center gap-2">
                                <input
                                    type="text"
                                    className={`w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400 ${isMobile ? 'py-3 text-base' : ''}`}
                                    placeholder="Search designs..."
                                    value={searchDesign}
                                    onChange={e => setSearchDesign(e.target.value)}
                                />
                            </div>

                            {/* Mobile List View for Designs */}
                            {isMobile ? (
                                <div className="space-y-3 max-h-[60vh] overflow-y-auto">
                                    {filteredDesigns.map(design => {
                                        const name = design.title || design.name;
                                        const isChecked = selectedDesigns.some(s => s.id === design.id);
                                        const type = design.type || design.card_type?.type_of_connection || design.card_type_name || "-";
                                        return (
                                            <div
                                                key={design.id}
                                                className={`border rounded-lg p-4 transition-all ${isChecked ? 'bg-blue-50 border-blue-300 dark:bg-blue-900/30 dark:border-blue-400' : 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-600'} active:scale-[0.98]`}
                                                onClick={() => {
                                                    if (isChecked) {
                                                        setSelectedDesigns(prev => prev.filter(s => s.id !== design.id));
                                                    } else {
                                                        setSelectedDesigns(prev => [...prev, design]);
                                                    }
                                                }}
                                            >
                                                <div className="flex items-center gap-3">
                                                    <input
                                                        type="checkbox"
                                                        checked={isChecked}
                                                        onChange={() => {}} // Handled by parent onClick
                                                        className="w-5 h-5 accent-blue-600 focus:ring-blue-400"
                                                        onClick={e => e.stopPropagation()}
                                                    />
                                                    {design.image || design.thumbnail ? (
                                                        <img src={design.image || design.thumbnail} alt={name} className="w-12 h-12 rounded-lg object-cover" />
                                                    ) : (
                                                        <div
                                                            className="w-12 h-12 rounded-lg flex items-center justify-center font-bold text-white text-lg shadow"
                                                            style={{
                                                                background: `linear-gradient(135deg, #7f9cf5 0%, #a78bfa 100%)`
                                                            }}
                                                        >
                                                            {name?.charAt(0)?.toUpperCase() || "?"}
                                                        </div>
                                                    )}
                                                    <div className="flex-1 min-w-0">
                                                        <h3 className="font-semibold text-gray-900 dark:text-white truncate">{name}</h3>
                                                        <p className="text-sm text-gray-500 dark:text-gray-300">{type}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                /* Desktop Table */
                                <div className="overflow-x-auto max-h-[350px]">
                                <table className="min-w-full border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800">
                                    <thead>
                                        <tr className="bg-gray-50 dark:bg-gray-700">
                                            <th className="p-2 text-left text-gray-900 dark:text-white"><input type="checkbox" checked={filteredDesigns.length > 0 && filteredDesigns.every(d => selectedDesigns.some(s => s.id === d.id))} onChange={e => {
                                                if (e.target.checked) {
                                                    setSelectedDesigns(filteredDesigns);
                                                } else {
                                                    setSelectedDesigns([]);
                                                }
                                            }} /></th>
                                            <th className="p-2 text-left text-gray-900 dark:text-white">Design Name</th>
                                            <th className="p-2 text-left text-gray-900 dark:text-white">Type</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredDesigns.map(design => {
                                            const name = design.title || design.name;
                                            const isChecked = selectedDesigns.some(s => s.id === design.id);
                                            const type = design.type || design.card_type?.type_of_connection || design.card_type_name || "-";
                                            return (
                                                <tr
                                                    key={design.id}
                                                    className={`border-b hover:bg-blue-50 dark:hover:bg-blue-900/20 transition cursor-pointer ${isChecked ? 'bg-blue-100 dark:bg-blue-900/30' : ''}`}
                                                    onClick={() => {
                                                        if (isChecked) {
                                                            setSelectedDesigns(prev => prev.filter(s => s.id !== design.id));
                                                        } else {
                                                            setSelectedDesigns(prev => [...prev, design]);
                                                        }
                                                    }}
                                                >
                                                    <td className="p-2" onClick={e => e.stopPropagation()}>
                                                        <input
                                                            type="checkbox"
                                                            checked={isChecked}
                                                            onChange={e => {
                                                                if (e.target.checked) {
                                                                    setSelectedDesigns(prev => [...prev, design]);
                                                                } else {
                                                                    setSelectedDesigns(prev => prev.filter(s => s.id !== design.id));
                                                                }
                                                            }}
                                                            className="accent-blue-600 focus:ring-blue-400"
                                                        />
                                                    </td>
                                                    <td className="p-2 flex items-center gap-3">
                                                        {design.image || design.thumbnail ? (
                                                            <img src={design.image || design.thumbnail} alt={name} className="w-10 h-10 rounded" />
                                                        ) : (
                                                            <div
                                                                className="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white text-lg shadow"
                                                                style={{
                                                                    background: `linear-gradient(135deg, #7f9cf5 0%, #a78bfa 100%)`
                                                                }}
                                                            >
                                                                {name?.charAt(0)?.toUpperCase() || "?"}
                                                            </div>
                                                        )}
                                                        <span className="font-medium text-gray-900 dark:text-white">{name}</span>
                                                    </td>
                                                    <td className="p-2 text-gray-700 dark:text-gray-300">{type}</td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>
                            )}

                            {/* Save Button */}
                            <div className={`mt-4 ${isMobile ? 'flex flex-col gap-3' : 'flex flex-col items-end gap-2'}`}>
                                {saveMessage && <div className="text-green-600 dark:text-green-400 font-medium mb-2">{saveMessage}</div>}
                                {saveError && <div className="text-red-600 dark:text-red-400 font-medium mb-2">{saveError}</div>}
                                <button
                                    className={`px-5 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition disabled:opacity-60 ${isMobile ? 'w-full py-3 text-base' : ''}`}
                                    onClick={handleSaveDesigns}
                                    disabled={saveLoading || selectedDesigns.length === 0}
                                >
                                    {saveLoading ? "Saving..." : "Save"}
                                </button>
                            </div>
                        </div>
                    )}
                </Dialog>
                    {/* Image Generation Modal */}
                    {showImageGenerationModal && (
                        <ImageGenerationModal
                            visible={showImageGenerationModal}
                            designId={currentDesignId}
                            batchId={currentBatchId}
                            useGroupsDesignApi={true} // Use getGroupsDesignStatus
                            groupIds={currentGroupId ? [currentGroupId] : []} // Pass the current group ID only if not null
                            onHide={() => {
                                setShowImageGenerationModal(false);
                                setCurrentBatchId(null);
                                setCurrentDesignId(null);
                                setCurrentGroupId(null);
                            }}
                        />
                    )}
            </div>

            {/* Cards Counter Component - Moved to bottom */}
            <div className="mt-12">
                {/* Cards Counter Component */}
            </div>

            {/* Mobile Floating Action Button (FAB) */}
            {isMobile && (
                <div className="mobile-fab-container">
                    <button
                        onClick={() => setisCreateGroupModalOpen(true)}
                        className="mobile-fab"
                        title="Create Group"
                    >
                        <FaPlus className="text-xl" />
                    </button>
                </div>
            )}

            {/* Members Modal - Full Page */}
            <Dialog
                visible={isMembersModalOpen}
                onHide={closeMembersModal}
                header={
                    <div className="flex items-center justify-between w-full">
                        <span className="text-gray-900 dark:text-gray-100 flex items-center gap-2">
                            Group Members - {selectedGroupForMembers?.title || 'Unknown Group'}
                            {hasUnsavedChanges && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300 animate-pulse">
                                    <i className="pi pi-clock mr-1"></i>
                                    Unsaved changes
                                </span>
                            )}
                        </span>
                        <button
                                onClick={() => {
                                    // Check if there are unsaved changes
                                    if (hasUnsavedChanges) {
                                        setShowExitWarningModal(true);
                                        setPendingNavigation(() => () => closeMembersModal());
                                    } else {
                                        // No unsaved changes, close directly
                                        closeMembersModal();
                                    }
                                }}
                                className="px-4 py-2 rounded-lg bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:shadow-md transition-all hover:translate-y-[-2px] flex items-center justify-center hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 font-medium text-sm"
                                title="Close Modal"
                            >
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className="mr-2">
                                <path d="M2.146 2.854a.5.5 0 0 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
                            </svg>
                            Close
                        </button>
                    </div>
                }
                style={{ 
                    width: '100vw', 
                    height: '100vh', 
                    maxWidth: '100vw',
                    maxHeight: '100vh',
                    margin: 0,
                    top: 0,
                    left: 0,
                    position: 'fixed',
                    zIndex: 9999
                }}
                modal
                className="group-members-modal-fullscreen"
                contentClassName="p-0 overflow-hidden h-full"
                maximizable={false}
                resizable={false}
                draggable={false}
                closable={false}
                footer={null}
            >
                {selectedGroupForMembers && (
                    <MembersDataTableProvider>
                        <div className="w-full h-full overflow-auto bg-gray-100 dark:bg-gray-800" style={{ 
                            minHeight: 'calc(100vh - 60px)',
                            height: 'calc(100vh - 60px)',
                            maxHeight: 'calc(100vh - 60px)',
                            width: '100%',
                            minWidth: '100%',
                            maxWidth: '100%'
                        }}>
                            <GroupMembersPage 
                                groupId={selectedGroupForMembers.id} 
                                onUnsavedChangesChange={setHasUnsavedChanges}
                            />
                        </div>
                    </MembersDataTableProvider>
                )}
            </Dialog>

            {/* Exit Warning Modal */}
            <Dialog
                visible={showExitWarningModal}
                onHide={handleCancelExit}
                modal
                closable={false}
                className="exit-warning-modal"
                style={{ 
                    width: '100vw', 
                    height: '100vh', 
                    maxWidth: '100vw',
                    maxHeight: '100vh',
                    margin: 0,
                    top: 0,
                    left: 0,
                    position: 'fixed',
                    zIndex: 9999
                }}
                contentClassName="p-0 overflow-hidden"
            >
                <div className="relative bg-white dark:bg-gray-800 p-10 rounded-lg">
                    {/* Close Button */}
                    <button
                        onClick={handleCancelExit}
                        className="absolute top-4 right-4 px-4 py-2 rounded-lg bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:shadow-md transition-all hover:translate-y-[-2px] flex items-center justify-center hover:bg-red-50 dark:hover:bg-red-900/20 hover:border-red-300 dark:hover:border-red-600 hover:text-red-600 dark:hover:text-red-400 z-20 font-medium text-sm"
                        title="Close Modal"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className="mr-2">
                            <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"/>
                        </svg>
                        Close
                    </button>
                    
                    {/* Animated Background Elements */}
                    <div className="absolute inset-0 overflow-hidden rounded-lg">
                        <div className="absolute -top-4 -right-4 w-24 h-24 bg-red-200 rounded-full opacity-20 animate-pulse"></div>
                        <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-orange-200 rounded-full opacity-15 animate-pulse" style={{ animationDelay: '1s' }}></div>
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-yellow-200 rounded-full opacity-10 animate-ping"></div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10">
                        {/* Warning Icon */}
                        <div className="flex justify-center mb-6">
                            <div className="relative">
                                <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                                    <i className="pi pi-exclamation-triangle text-white text-3xl"></i>
                                </div>
                                <div className="absolute inset-0 w-20 h-20 bg-gradient-to-br from-red-500 to-orange-500 rounded-full animate-ping opacity-25"></div>
                            </div>
                        </div>

                        {/* Title */}
                        <h2 className="text-2xl font-bold text-center text-gray-800 dark:text-gray-100 mb-4">
                            ⚠️ Unsaved Changes
                        </h2>

                        {/* Message */}
                        <div className="text-center mb-6">
                            <p className="text-gray-700 dark:text-gray-300 text-lg mb-3">
                                You have unsaved changes in the group members page.
                            </p>
                            <p className="text-gray-600 dark:text-gray-400 text-sm">
                                If you leave now, all unsaved changes will be lost.
                            </p>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col gap-6">
                            {/* Save and Continue Button */}
                            <button
                                onClick={handleSaveAndExit}
                                className="group relative overflow-hidden px-8 py-4 rounded-xl font-bold text-white transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 active:scale-95 flex items-center justify-center text-lg"
                                style={{
                                    background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                                    boxShadow: '0 10px 25px rgba(16, 185, 129, 0.4)'
                                }}
                                onMouseEnter={(e) => {
                                    e.target.style.boxShadow = '0 20px 40px rgba(16, 185, 129, 0.6)';
                                }}
                                onMouseLeave={(e) => {
                                    e.target.style.boxShadow = '0 10px 25px rgba(16, 185, 129, 0.4)';
                                }}
                            >
                                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                                <i className="pi pi-save mr-3 text-lg group-hover:animate-bounce"></i>
                                <span className="relative z-10">Save Changes & Continue</span>
                            </button>

                            {/* Discard and Leave Button */}
                            <button
                                onClick={handleConfirmExit}
                                className="group relative overflow-hidden px-8 py-4 rounded-xl font-bold text-white transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 active:scale-95 flex items-center justify-center text-lg"
                                style={{
                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                    boxShadow: '0 10px 25px rgba(239, 68, 68, 0.4)'
                                }}
                                onMouseEnter={(e) => {
                                    e.target.style.boxShadow = '0 20px 40px rgba(239, 68, 68, 0.6)';
                                }}
                                onMouseLeave={(e) => {
                                    e.target.style.boxShadow = '0 10px 25px rgba(239, 68, 68, 0.4)';
                                }}
                            >
                                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                                <i className="pi pi-trash mr-3 text-lg group-hover:animate-pulse"></i>
                                <span className="relative z-10">Discard Changes & Leave</span>
                            </button>

                            {/* Cancel Button */}
                            <button
                                onClick={handleCancelExit}
                                className="px-8 py-4 rounded-xl font-bold text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-600 border-2 border-gray-300 dark:border-gray-500 hover:bg-gray-200 dark:hover:bg-gray-500 hover:border-gray-400 dark:hover:border-gray-400 transition-all duration-300 flex items-center justify-center text-lg"
                            >
                                <i className="pi pi-times mr-3"></i>
                                Stay on Page
                            </button>
                        </div>
                    </div>
                </div>
            </Dialog>
            </React.Fragment>
    );
}

export default GroupsDataTable;