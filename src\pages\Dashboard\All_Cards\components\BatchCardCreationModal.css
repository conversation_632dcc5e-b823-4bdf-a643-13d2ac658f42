/* Batch Card Creation Modal Styles */
.batch-card-creation-dialog .p-dialog-content {
  padding: 0 !important;
}

.batch-card-creation-dialog .p-dialog-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .batch-card-creation-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .batch-card-creation-dialog .p-dialog-header {
    padding: 1rem;
  }

  /* Mobile step indicators */
  .batch-card-creation-dialog .step-indicator {
    width: 8px;
    height: 8px;
  }

  .batch-card-creation-dialog .step-connector {
    width: 8px;
  }

  /* Mobile card list */
  .batch-card-creation-dialog .scanned-cards-list {
    max-height: 200px;
  }

  /* Mobile buttons */
  .batch-card-creation-dialog .action-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .batch-card-creation-dialog .action-buttons > div {
    width: 100%;
    justify-content: center;
  }

  .batch-card-creation-dialog .action-buttons button {
    width: 100%;
    justify-content: center;
  }
}

/* Progress step animations */
.step-indicator {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-indicator.active {
  transform: scale(1.1);
  box-shadow: 0 0 0 4px rgba(0, 195, 172, 0.2);
}

.step-connector {
  transition: all 0.5s ease;
}

/* Card list animations */
.scanned-card-item {
  transition: all 0.2s ease;
}

.scanned-card-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Scanner button pulse animation */
@keyframes pulse-scanner {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

.scanner-button {
  animation: pulse-scanner 2s infinite;
}

/* Custom scrollbar for card lists */
.scanned-cards-list::-webkit-scrollbar {
  width: 6px;
}

.scanned-cards-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.scanned-cards-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.scanned-cards-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading state styles */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 16px;
}

/* Success/Error states */
.status-indicator {
  transition: all 0.3s ease;
}

.status-indicator.success {
  color: #10b981;
  background-color: #d1fae5;
}

.status-indicator.error {
  color: #ef4444;
  background-color: #fee2e2;
}

.status-indicator.warning {
  color: #f59e0b;
  background-color: #fef3c7;
}

/* Dark mode support */
.dark .batch-card-creation-dialog .p-dialog {
  background-color: #1e293b !important;
  border: 1px solid #334155 !important;
}

.dark .batch-card-creation-dialog .p-dialog-header {
  background-color: #1e293b !important;
  border-bottom-color: #334155 !important;
  color: #f1f5f9 !important;
}

.dark .batch-card-creation-dialog .p-dialog-content {
  background-color: #1e293b !important;
  color: #f1f5f9 !important;
}

/* Dark mode step indicators */
.dark .step-indicator {
  background-color: #334155 !important;
  color: #94a3b8 !important;
}

.dark .step-indicator.active {
  background-color: #00c3ac !important;
  color: white !important;
}

.dark .step-connector {
  background-color: #334155 !important;
}

.dark .step-connector.active {
  background-color: #00c3ac !important;
}

/* Dark mode cards */
.dark .scanned-card-item {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: #f1f5f9 !important;
}

.dark .scanned-card-item:hover {
  background-color: #475569 !important;
}

/* Dark mode buttons */
.dark .batch-card-creation-dialog button {
  color: #f1f5f9 !important;
}

.dark .batch-card-creation-dialog .bg-gray-100 {
  background-color: #334155 !important;
  color: #f1f5f9 !important;
}

.dark .batch-card-creation-dialog .bg-gray-100:hover {
  background-color: #475569 !important;
}

/* Dark mode summary boxes */
.dark .bg-gray-50 {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

/* Accessibility improvements */
.batch-card-creation-dialog button:focus {
  outline: 2px solid #00c3ac;
  outline-offset: 2px;
}

.batch-card-creation-dialog .step-indicator:focus {
  outline: 2px solid #00c3ac;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .batch-card-creation-dialog {
    border: 2px solid #000;
  }
  
  .step-indicator {
    border: 2px solid #000;
  }
  
  .scanned-card-item {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .step-indicator,
  .step-connector,
  .scanned-card-item,
  .scanner-button {
    transition: none;
    animation: none;
  }
}

/* Print styles */
@media print {
  .batch-card-creation-dialog {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .batch-card-creation-dialog .action-buttons {
    display: none;
  }
}
