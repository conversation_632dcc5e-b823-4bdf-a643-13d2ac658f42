import { useState, useRef, useEffect } from 'react';
import { Tooltip } from 'primereact/tooltip';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dropdown } from 'primereact/dropdown';
import { TfiTrash } from 'react-icons/tfi';
import { FaSearch } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { HiQrCode } from 'react-icons/hi2';
import Container from '@components/Container';
import CreateCardForm from '../Backages/CreateCardForm';
import BatchCardCreationModal from './components/BatchCardCreationModal';
import { useFetchCards } from "../../../quires/useGetCards ";
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { useLayout } from '../../../contexts/LayoutContext';
import { motion } from 'framer-motion';

const CardsDataTable = () => {
  const { data: cards, isLoading, isError, error, refetch } = useFetchCards();
  const { isMobile } = useLayout();
  const [isCreateCardModalOpen, setIsCreateCardModalOpen] = useState(false);
  const [isBatchCreateModalOpen, setIsBatchCreateModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [assignmentFilter, setAssignmentFilter] = useState('all');
  const [filteredCards, setFilteredCards] = useState([]);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");
  const [rowsPerPage] = useState(10);
  const rowsPerPageOptions = [5, 25, 50, 100];

  // Assignment filter options
  const assignmentOptions = [
    { label: 'All Cards', value: 'all' },
    { label: 'Assigned Cards', value: 'assigned' },
    { label: 'Unassigned Cards', value: 'unassigned' }
  ];

  // Filter cards based on search query and assignment status
  useEffect(() => {
    const cardData = cards?.data || [];
    let filtered = [...cardData];

    // Apply search filter
    if (searchQuery.trim()) {
      const searchLower = searchQuery.toLowerCase();
      filtered = filtered.filter(card => {
        return (
          card.name?.toLowerCase().includes(searchLower) ||
          card.number?.toLowerCase().includes(searchLower) ||
          card.manager_name?.toLowerCase().includes(searchLower) ||
          card.card_type?.name?.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply assignment filter
    if (assignmentFilter !== 'all') {
      filtered = filtered.filter(card => {
        const isAssigned = card.manager_name && card.manager_name !== 'No Manager' && card.manager_name !== '';
        return assignmentFilter === 'assigned' ? isAssigned : !isAssigned;
      });
    }

    setFilteredCards(filtered);
  }, [cards?.data, searchQuery, assignmentFilter]);

  const openCreateCardModal = () => {
    setIsEditMode(false);
    setIsCreateCardModalOpen(true);
  };

  const openBatchCreateModal = () => {
    setIsBatchCreateModalOpen(true);
  };

  const handleEdit = (rowData) => {
    setEditData(rowData);
    setIsEditMode(true);
    setIsCreateCardModalOpen(true);
  };

  const resetEditMode = () => {
    setIsEditMode(false);
    setEditData(null);
  };

  const handleDelete = (id) => {
    confirmDialog({
      message: 'Are you sure you want to delete this card?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteCard(id),
      reject: () => {}
    });
  };

  const deleteCard = async (id) => {
    try {
      const response = await fetch(`${backendUrl}/cards/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Card deleted successfully',
          life: 3000
        });
        refetch();
      } else {
        const errorData = await response.json();
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: errorData.message || 'Failed to delete card',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while deleting card',
        life: 3000
      });
    }
  };

  const actionsBodyTemplate = (rowData) => (
    <div className="flex justify-around">
      <Tooltip target=".edit-icon" content="Edit" position="top" />
      <button className="edit-icon" onClick={() => handleEdit(rowData)}>
        <FiEdit className="text-yellow-500" size={20} />
      </button>

      <Tooltip target=".delete-icon" content="Delete" position="top" />
      <button className="delete-icon" onClick={() => handleDelete(rowData.id)}>
        <TfiTrash className="text-red-500" size={20} />
      </button>
    </div>
  );

  if (isLoading) return <p>Loading...</p>;
  if (isError) return <p>Error: {error.message}</p>;

  return (
    <Container className="w-full">
      <Toast ref={toast} />
      <ConfirmDialog />
      
      {/* Professional Header Section */}
      <div className="w-full mb-8">
        <div className="w-full">
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-800 dark:via-gray-700 dark:to-gray-600 shadow-xl border border-gray-200 dark:border-gray-600 all-cards-header-container">
            {/* Animated Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500"></div>
              <div className="absolute top-10 left-10 w-20 h-20 bg-blue-300 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute top-20 right-20 w-16 h-16 bg-purple-300 rounded-full blur-xl animate-pulse delay-1000"></div>
              <div className="absolute bottom-10 left-1/4 w-12 h-12 bg-pink-300 rounded-full blur-xl animate-pulse delay-2000"></div>
            </div>
            
            {/* Floating Elements */}
            <div className="absolute top-4 right-4 w-8 h-8 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 animate-bounce"></div>
            <div className="absolute bottom-4 left-4 w-6 h-6 bg-purple-200 dark:bg-purple-800 rounded-full opacity-30 animate-bounce delay-500"></div>
            <div className="absolute top-1/2 right-1/4 w-4 h-4 bg-indigo-200 dark:bg-indigo-800 rounded-full opacity-25 animate-bounce delay-1000"></div>
            
            <div className="relative z-10 p-4">
              <div className={`${isMobile ? 'flex flex-col space-y-3 mb-4' : 'flex flex-row items-center justify-between mb-4'}`}>
                <div className={`${isMobile ? 'w-full' : 'mb-0'}`}>
                  <div className={`flex items-center gap-3 ${isMobile ? 'justify-center' : ''}`}>
                    <div className="relative">
                      <div className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg`}>
                        <svg className={`text-white ${isMobile ? 'text-base' : 'text-lg'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                      </div>
                      <div className={`absolute -top-1 -right-1 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'} bg-green-500 rounded-full flex items-center justify-center border-2 border-white shadow-md`}>
                        <span className={`text-white ${isMobile ? 'text-xs' : 'text-xs'} font-bold`}>
                          {(filteredCards?.length || 0).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className={`${isMobile ? 'text-center' : ''}`}>
                      <h1 className={`${isMobile ? 'text-lg' : 'text-xl lg:text-2xl'} font-bold text-gray-900 dark:text-gray-100 mb-0.5`}>
                        All Cards Management
                      </h1>
                      <p className={`text-gray-600 dark:text-gray-400 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        {isMobile ? 'Manage all cards' : 'View and manage all cards in the system'}
                      </p>
                    </div>
                  </div>
                </div>
                {!isMobile && (
                  <div className="flex items-center gap-3">
                    <motion.button
                      onClick={openCreateCardModal}
                      className="group relative px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-emerald-400/30"
                    >
                      <div className="relative">
                        <span className="text-base group-hover:rotate-90 transition-transform duration-300">+</span>
                      </div>
                      <span className="text-base">Create New Card</span>
                      <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-teal-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </motion.button>

                    <motion.button
                      onClick={openBatchCreateModal}
                      className="group relative px-6 py-3 bg-white text-gray-800 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 flex items-center gap-2 border border-gray-200 hover:border-gray-300"
                    >
                      <div className="relative">
                        <HiQrCode className="text-lg group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <span className="text-base">Create New Batch</span>
                      <div className="absolute inset-0 bg-gray-100/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </motion.button>
                  </div>
                )}
              </div>
              
              {/* Stats Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="group relative bg-white dark:bg-gray-700/80 backdrop-blur-sm rounded-xl p-4 border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <svg className="text-blue-600 group-hover:text-blue-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6" width={isMobile ? 14 : 20} height={isMobile ? 14 : 20} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        <div className="absolute inset-0 bg-blue-100 dark:bg-blue-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                      </div>
                      <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-900 dark:text-gray-100 group-hover:text-blue-700 transition-colors duration-300`}>
                        {(filteredCards?.length || 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-blue-600 transition-colors duration-300`}>
                    {isMobile ? 'Total' : 'Total Cards'}
                  </div>
                </div>
                
                <div className="group relative bg-white dark:bg-gray-700/80 backdrop-blur-sm rounded-xl p-4 border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <svg className="text-green-600 group-hover:text-green-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6" width={isMobile ? 14 : 20} height={isMobile ? 14 : 20} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div className="absolute inset-0 bg-green-100 dark:bg-green-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                      </div>
                      <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-green-600 dark:text-green-400 group-hover:text-green-700 transition-colors duration-300`}>
                        {(filteredCards?.filter(card => card.manager_name && card.manager_name !== 'No Manager' && card.manager_name !== '')?.length || 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-green-600 transition-colors duration-300`}>Assigned</div>
                </div>
                
                <div className="group relative bg-white dark:bg-gray-700/80 backdrop-blur-sm rounded-xl p-4 border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <svg className="text-purple-600 group-hover:text-purple-700 transition-colors duration-300 group-hover:scale-110 group-hover:rotate-6" width={isMobile ? 14 : 20} height={isMobile ? 14 : 20} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div className="absolute inset-0 bg-purple-100 dark:bg-purple-900 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 scale-150"></div>
                      </div>
                      <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-purple-600 dark:text-purple-400 group-hover:text-purple-700 transition-colors duration-300`}>
                        {(filteredCards?.filter(card => !card.manager_name || card.manager_name === 'No Manager' || card.manager_name === '')?.length || 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-600 dark:text-gray-400 group-hover:text-purple-600 transition-colors duration-300`}>Unassigned</div>
                </div>
              </div>
              
              {/* Quick Actions */}
              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>Showing {(filteredCards?.length || 0).toLocaleString()} cards</span>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>System Active</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Mobile FAB */}
      {isMobile && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
          {/* Batch Create FAB */}
          <motion.button
            onClick={openBatchCreateModal}
            className="w-14 h-14 bg-white text-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center border border-gray-200"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            title="Create New Batch"
          >
            <HiQrCode className="text-xl" />
          </motion.button>

          {/* Single Create FAB */}
          <motion.button
            onClick={openCreateCardModal}
            className="w-14 h-14 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center border border-emerald-400/30"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            title="Create New Card"
          >
            <span className="text-xl font-bold">+</span>
          </motion.button>
        </div>
      )}

      {/* Search and Filter Section */}
      <div className={`w-full mb-4 mt-1 ${isMobile ? 'px-2 space-y-3' : 'flex justify-center items-center gap-4'}`}>
        {/* Search Bar */}
        <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[500px]'}`}>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search by card name, number, manager, or card type..."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Assignment Filter */}
        <div className={`${isMobile ? 'w-full' : 'w-48'}`}>
          <Dropdown
            value={assignmentFilter}
            options={assignmentOptions}
            onChange={(e) => setAssignmentFilter(e.value)}
            placeholder="Filter by Assignment"
            className="w-full border rounded-md shadow-md"
            panelClassName="shadow-lg"
          />
        </div>

     
      </div>

      <CreateCardForm
        isModalOpen={isCreateCardModalOpen}
        setIsModalOpen={setIsCreateCardModalOpen}
        fetchCards={refetch}
        editData={editData}
        isEditMode={isEditMode}
        resetEditMode={resetEditMode}
        lazy
        filterDisplay="row"
        responsiveLayout="stack"
        breakpoint="960px"
        dataKey="id"
        paginator
        className="table border"

        rowsPerPageOptions={[5, 25, 50, 100]}

        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"

        scrollable
        scrollHeight="calc(100vh - 400px)"
      />

      <BatchCardCreationModal
        isOpen={isBatchCreateModalOpen}
        onClose={() => setIsBatchCreateModalOpen(false)}
        onBatchCreated={refetch}
        toast={toast}
      />

      <DataTable value={filteredCards} paginator rows={rowsPerPage} rowsPerPageOptions={rowsPerPageOptions} className="mt-4"
                        scrollable
                  scrollHeight="100%"
      >
        <Column field="name" header="Card Name" />
        <Column field="number" header="Card Number" />
        <Column field="card_type.type_of_connection" header="Card Type of Connection" />
        <Column field="card_type.name" header="Card Type" />
        <Column
          field="manager_name"
          header="Manager Name"
          className="text-left"
          body={(rowData) => (
            <div className="flex items-center gap-2">
              <span
                className={`inline-block w-2 h-2 rounded-full ${
                  rowData.manager_name && rowData.manager_name !== 'No Manager' && rowData.manager_name !== ''
                    ? 'bg-green-500'
                    : 'bg-gray-400'
                }`}
                title={rowData.manager_name && rowData.manager_name !== 'No Manager' && rowData.manager_name !== '' ? 'Assigned' : 'Unassigned'}
              />
              <span>{rowData.manager_name || 'Unassigned'}</span>
            </div>
          )}
        />

        <Column body={actionsBodyTemplate} header="Actions" />
      </DataTable>
    </Container>
  );
};

export default CardsDataTable;